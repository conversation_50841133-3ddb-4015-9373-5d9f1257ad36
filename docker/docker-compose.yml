services:
  mysql:
    container_name: bisheng-mysql
    image: mysql:8.0
    
    ports:
      - "3307:3306"
    environment:
      MYSQL_ROOT_PASSWORD: "1234"  # 数据库密码，如果修改需要同步修改bisheng/congfig/config.yaml配置database_url的mysql连接密码
      MYSQL_DATABASE: bisheng
      TZ: Asia/Shanghai
    volumes:
      - ${DOCKER_VOLUME_DIRECTORY:-.}/mysql/conf/my.cnf:/etc/mysql/my.cnf
      - ${DOCKER_VOLUME_DIRECTORY:-.}/mysql/data:/var/lib/mysql
    healthcheck:
      test: ["CMD-SHELL", "exit | mysql -u root -p$$MYSQL_ROOT_PASSWORD"]
      start_period: 30s
      interval: 20s
      timeout: 10s
      retries: 4
    restart: on-failure

  redis:
    container_name: bisheng-redis
    image: redis:7.0.4
    ports:
      - "6380:6379"
    environment:
      TZ: Asia/Shanghai
    volumes:
      - ${DOCKER_VOLUME_DIRECTORY:-.}/data/redis:/data
      - ${DOCKER_VOLUME_DIRECTORY:-.}/redis/redis.conf:/etc/redis.conf
    command: redis-server /etc/redis.conf
    healthcheck:
      test: ["CMD-SHELL", 'redis-cli ping|grep -e "PONG\|NOAUTH"']
      interval: 10s
      timeout: 5s
      retries: 3
    restart: on-failure

  office:
    container_name: bisheng-office
    image: onlyoffice/documentserver:7.1.1
    ports:
      - "8701:80"
    environment:
      TZ: Asia/Shanghai
      JWT_ENABLED: "false"
    volumes:
      - ${DOCKER_VOLUME_DIRECTORY:-.}/office/bisheng:/var/www/onlyoffice/documentserver/sdkjs-plugins/bisheng
    command: bash -c "supervisorctl restart all"
    restart: on-failure

  backend:
    container_name: bisheng-backend
    image: dataelement/bisheng-backend:v2.0.2
    ports:
      - "7861:7860"
    environment:
      TZ: Asia/Shanghai
      BS_MILVUS_CONNECTION_ARGS: '{"host":"**************","port":"19530","user":"","password":"","secure":false}'
      BS_MILVUS_IS_PARTITION: 'true'
      BS_MILVUS_PARTITION_SUFFIX: '1'
      BS_ELASTICSEARCH_URL: 'http://elasticsearch:9200'
      BS_ELASTICSEARCH_SSL_VERIFY: '{}'  # 可根据自己部署的密码进行配置 '{"basic_auth": ("elastic", "elastic")}'
      BS_MINIO_SCHEMA: 'false'
      BS_MINIO_CERT_CHECK: 'false'
      BS_MINIO_ENDPOINT: 'localhost:9000'
      BS_MINIO_SHAREPOINT: 'localhost:9000'
      BS_MINIO_ACCESS_KEY: 'minioadmin'
      BS_MINIO_SECRET_KEY: 'minioadmin'
    volumes:
      - ${DOCKER_VOLUME_DIRECTORY:-.}/bisheng/config/config.yaml:/app/bisheng/config.yaml
      - ${DOCKER_VOLUME_DIRECTORY:-.}/bisheng/entrypoint.sh:/app/entrypoint.sh
      - ${DOCKER_VOLUME_DIRECTORY:-.}/data/bisheng:/app/data
    security_opt:
      - seccomp:unconfined
    command: sh entrypoint.sh api  # 启动api服务
    restart: on-failure
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7860/health"]
      start_period: 30s
      interval: 90s
      timeout: 30s
      retries: 3
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      office:
        condition: service_started
  
  backend_worker:
    container_name: bisheng-backend-worker
    image: dataelement/bisheng-backend:v2.0.2
    environment:
      TZ: Asia/Shanghai
      BS_MILVUS_CONNECTION_ARGS: '{"host":"**************","port":"19530","user":"","password":"","secure":false}'
      BS_MILVUS_IS_PARTITION: 'true'
      BS_MILVUS_PARTITION_SUFFIX: '1'
      BS_ELASTICSEARCH_URL: 'http://elasticsearch:9200'
      BS_ELASTICSEARCH_SSL_VERIFY: '{}'  # 可根据自己部署的密码进行配置 '{"basic_auth": ("elastic", "elastic")}'
      BS_MINIO_SCHEMA: 'false'
      BS_MINIO_CERT_CHECK: 'false'
      BS_MINIO_ENDPOINT: '**************:9000'
      BS_MINIO_SHAREPOINT: '**************:9000'
      BS_MINIO_ACCESS_KEY: 'minioadmin'
      BS_MINIO_SECRET_KEY: 'minioadmin'
    volumes:
      - ${DOCKER_VOLUME_DIRECTORY:-.}/bisheng/config/config.yaml:/app/bisheng/config.yaml
      - ${DOCKER_VOLUME_DIRECTORY:-.}/bisheng/entrypoint.sh:/app/entrypoint.sh
      - ${DOCKER_VOLUME_DIRECTORY:-.}/data/bisheng:/app/data
    security_opt:
      - seccomp:unconfined
    command: sh entrypoint.sh worker  # 启动celery的异步worker服务，用来处理一些耗时的任务
    restart: on-failure
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      office:
        condition: service_started

  frontend:
    container_name: bisheng-frontend
    image: dataelement/bisheng-frontend:v2.0.2
    ports:
      - "3001:3001"
    environment:
      TZ: Asia/Shanghai
    volumes:
      - ${DOCKER_VOLUME_DIRECTORY:-.}/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ${DOCKER_VOLUME_DIRECTORY:-.}/nginx/conf.d:/etc/nginx/conf.d
    restart: on-failure
    depends_on:
      - backend

  elasticsearch:
    container_name: bisheng-es
    image: docker.io/bitnami/elasticsearch:8.12.0
    user: root
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      TZ: Asia/Shanghai
    volumes:
      - ${DOCKER_VOLUME_DIRECTORY:-.}/data/es:/bitnami/elasticsearch/data
    restart: on-failure


