
# 在http区域内一定要添加下面配置, 支持websocket
map $http_upgrade $connection_upgrade {
	default upgrade;
	'' close;
}



server {
	gzip on;
	gzip_comp_level  2;
	gzip_min_length  1000;
	gzip_types  text/xml text/css;
	gzip_http_version 1.1;
	gzip_vary  on;
	gzip_disable "MSIE [4-6] \.";

	listen 3001;

	 location / {
		root /usr/share/nginx/html/platform;
		index index.html index.htm;
		try_files $uri $uri/ /index.html =404;
		add_header X-Frame-Options SAMEORIGIN;
	}

	location /workspace/ {
		alias /usr/share/nginx/html/client/;
		index index.html index.htm;
		try_files $uri $uri/ /workspace/index.html;
	}

	location ~ ^(/workspace)?/api(/|$) {
		rewrite ^/workspace(/.*)$ $1 break;
		proxy_pass http://backend:7860;
		proxy_read_timeout 300s;
		proxy_set_header Host $host;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_http_version 1.1;
		proxy_set_header Upgrade $http_upgrade;
		proxy_set_header Connection $connection_upgrade;
		client_max_body_size 200m;
		add_header Access-Control-Allow-Origin $host;
		add_header X-Frame-Options SAMEORIGIN;
	}

	location ~ ^(/workspace)?/bisheng|/tmp-dir {
		rewrite ^/workspace(/.*)$ $1 break;
		proxy_pass http://**************:9000;
	}
}