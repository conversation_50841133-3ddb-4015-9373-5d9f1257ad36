name: CI

on:
  push:
    # Sequence of patterns matched against refs/tags
    tags:
      - "v*"

env:
  DOCKERHUB_REPO: dataelement/
  
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
        
  build_bisheng_backend:
    runs-on: ubuntu-latest
    # if: startsWith(github.event.ref, 'refs/tags')
    steps:
      - name: checkout
        uses: actions/checkout@v2

      - name: Get version
        id: get_version
        run: |
          echo ::set-output name=VERSION::${GITHUB_REF/refs\/tags\//}
      
      - name: Set Environment Variable
        run: echo "RELEASE_VERSION=1.3.1" >> $GITHUB_ENV


      # 登录 docker hub
      - name: Login to DockerHub
        uses: docker/login-action@v1
        with:
          # GitHub Repo => Settings => Secrets 增加 docker hub 登录密钥信息
          # DOCKERHUB_USERNAME 是 docker hub 账号名.
          # DOCKERHUB_TOKEN: docker hub => Account Setting => Security 创建.
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
          
      # 构建 backend 并推送到 Docker hub
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v1

      - name: set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build backend and push
        id: docker_build_backend
        run: |
          docker buildx build --file ./src/backend/Dockerfile --platform linux/amd64 --provenance false --tag ${{ env.DOCKERHUB_REPO }}bisheng-backend:${{ steps.get_version.outputs.VERSION }}-amd64 --push ./src/backend/
      
  build_backend_arm:
    runs-on: ubuntu-22.04-arm
    steps:
      - name: checkout
        uses: actions/checkout@v2

      - name: Get version
        id: get_version
        run: |
          echo ::set-output name=VERSION::${GITHUB_REF/refs\/tags\//}
      
      - name: Set Environment Variable
        run: echo "RELEASE_VERSION=1.3.1" >> $GITHUB_ENV

      # 登录 docker hub
      - name: Login to DockerHub
        uses: docker/login-action@v1
        with:
          # GitHub Repo => Settings => Secrets 增加 docker hub 登录密钥信息
          # DOCKERHUB_USERNAME 是 docker hub 账号名.
          # DOCKERHUB_TOKEN: docker hub => Account Setting => Security 创建.
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
  
      # - name: Set up QEMU
      #   uses: docker/setup-qemu-action@v1

      - name: set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build backend and push
        id: docker_build_backend
        run: |
          docker buildx build --file ./src/backend/Dockerfile --platform linux/arm64 --provenance false --tag ${{ env.DOCKERHUB_REPO }}bisheng-backend:${{ steps.get_version.outputs.VERSION }}-arm64 --push ./src/backend/

  build_bisheng_frontend:
    runs-on: ubuntu-latest
    steps:
      - name: checkout
        uses: actions/checkout@v2

      - name: Get version
        id: get_version
        run: |
          echo ::set-output name=VERSION::${GITHUB_REF/refs\/tags\//}
      
      - name: Set Environment Variable
        run: echo "RELEASE_VERSION=${{ steps.get_version.outputs.VERSION }}" >> $GITHUB_ENV

      # 登录 docker hub
      - name: Login to DockerHub
        uses: docker/login-action@v1
        with:
          # GitHub Repo => Settings => Secrets 增加 docker hub 登录密钥信息
          # DOCKERHUB_USERNAME 是 docker hub 账号名.
          # DOCKERHUB_TOKEN: docker hub => Account Setting => Security 创建.
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build frontend and push
        id: docker_build_frontend
        run: |
          docker buildx build --file ./src/frontend/Dockerfile --platform linux/amd64 --provenance false --tag ${{ env.DOCKERHUB_REPO }}bisheng-frontend:${{ steps.get_version.outputs.VERSION }}-amd64 --push ./src/frontend/

  build_frontend_arm:
    runs-on: ubuntu-22.04-arm
    steps:
      - name: checkout
        uses: actions/checkout@v2

      - name: Get version
        id: get_version
        run: |
          echo ::set-output name=VERSION::${GITHUB_REF/refs\/tags\//}
      
      - name: Set Environment Variable
        run: echo "RELEASE_VERSION=${{ steps.get_version.outputs.VERSION }}" >> $GITHUB_ENV

      # - name: Set up QEMU
      #   uses: docker/setup-qemu-action@v1

      - name: set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # 登录 docker hub
      - name: Login to DockerHub
        uses: docker/login-action@v1
        with:
          # GitHub Repo => Settings => Secrets 增加 docker hub 登录密钥信息
          # DOCKERHUB_USERNAME 是 docker hub 账号名.
          # DOCKERHUB_TOKEN: docker hub => Account Setting => Security 创建.
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build frontend and push
        id: docker_build_frontend
        run: |
          docker buildx build --file ./src/frontend/Dockerfile --platform linux/arm64 --provenance false --tag ${{ env.DOCKERHUB_REPO }}bisheng-frontend:${{ steps.get_version.outputs.VERSION }}-arm64 --push ./src/frontend/

  notify_feishu:
    needs:
      - build_bisheng_backend
      - build_backend_arm
      - build_bisheng_frontend
      - build_frontend_arm
    runs-on: ubuntu-latest
    steps:
      - name: Get version
        id: get_version
        run: |
          echo ::set-output name=VERSION::${GITHUB_REF/refs\/tags\//}

      - name: Process git message
        id: process_message
        run: |
          value=$(echo "${{ github.event.head_commit.message }}" | sed -e ':a' -e 'N' -e '$!ba' -e 's/\n/%0A/g')
          value=$(echo "${value}" | sed -e ':a' -e 'N' -e '$!ba' -e 's/\r/%0A/g')
          echo "message=${value}" >> $GITHUB_ENV
        shell: bash

      - name: notify feishu
        uses: fjogeleit/http-request-action@v1
        with:
          url: ${{ secrets.FEISHU_WEBHOOK }}
          method: 'POST'
          data: '{"msg_type":"post","content":{"post":{"zh_cn":{"title": "${{ steps.get_version.outputs.VERSION }}-amd64镜像预发布成功",  "content": [[{"tag":"text","text":"发布功能："},{"tag":"text","text":"${{ env.message }}"}]]}}}}'
  