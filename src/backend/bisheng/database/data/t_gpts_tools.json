[{"name": "时间", "logo": null, "desc": "返回当前时间，如果未指定时区，将使用 UTC+8 时间", "tool_key": "get_current_time", "type": 1, "extra": "{}", "is_preset": 1, "is_delete": 0, "user_id": null, "create_time": "2024-03-29 14:36:00", "update_time": "2024-03-29 14:36:00", "id": 1, "api_params": []}, {"name": "计算器", "logo": null, "desc": "使用 Python 内置工具进行数学表达式计算", "tool_key": "calculator", "type": 2, "extra": "{}", "is_preset": 1, "is_delete": 0, "user_id": null, "create_time": "2024-03-29 14:37:11", "update_time": "2024-03-29 14:37:11", "id": 2, "api_params": []}, {"name": "论文获取", "logo": null, "desc": "从 Arxiv 网站检索论文的工具，输入为检索关键词。", "tool_key": "arxiv", "type": 3, "extra": "{}", "is_preset": 1, "is_delete": 0, "user_id": null, "create_time": "2024-03-29 14:39:37", "update_time": "2024-03-29 14:39:37", "id": 3, "api_params": [{"name": "query", "description": "search query to look up", "in": "query", "required": true, "schema": {"type": "string"}}]}, {"name": "Dalle3绘画", "logo": null, "desc": "根据提示词生成图像。", "tool_key": "dalle_image_generator", "type": 4, "is_preset": 1, "is_delete": 0, "user_id": null, "create_time": "2024-03-29 14:40:32", "update_time": "2024-03-29 14:40:32", "id": 4, "api_params": [{"name": "query", "description": "Description about image.", "in": "query", "required": true, "schema": {"type": "string"}}]}, {"name": "Bing web搜索", "logo": null, "desc": "使用 query 进行 Bing 搜索并返回返回结果。", "tool_key": "bing_search", "type": 5, "is_preset": 1, "is_delete": 0, "user_id": null, "create_time": "2024-03-29 14:41:16", "update_time": "2024-03-29 14:41:16", "id": 5, "api_params": [{"name": "query", "description": "query to look up in Bing search", "in": "query", "required": true, "schema": {"type": "string"}}]}, {"name": "代码执行器", "logo": null, "desc": "可以执行 Python  和 shell 代码，并在 600 秒内返回结果，适合处理数学、编程、图片和文件等问题场景。", "tool_key": "bisheng_code_interpreter", "type": 6, "is_preset": 1, "is_delete": 0, "user_id": null, "create_time": "2024-03-29 14:42:17", "update_time": "2024-03-29 14:42:17", "id": 6, "api_params": [{"name": "python_code", "description": "The pure python script to be evaluated. \\nThe contents will be in main.py. \\nIt should not be in markdown format.", "in": "query", "required": true, "schema": {"type": "string"}}]}, {"name": "人员所有公司", "logo": null, "desc": "可以通过公司名称或ID和人名获取企业人员的所有相关公司，包括其担任法人、股东、董监高的公司信息", "tool_key": "tianyancha_all_companys_by_company", "type": 7, "is_preset": 1, "is_delete": 0, "user_id": null, "create_time": "2024-03-29 14:44:34", "update_time": "2024-03-29 14:44:34", "id": 7, "api_params": [{"name": "query", "description": "human who you want to search", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "name", "description": "company name which human worked", "in": "query", "required": true, "schema": {"type": "string"}}]}, {"name": "搜索企业", "logo": null, "desc": "可以通过关键词获取企业列表，企业列表包括公司名称或ID、类型、成立日期、经营状态、统一社会信用代码等字段的详细信息", "tool_key": "tianyancha_search_company", "type": 7, "is_preset": 1, "is_delete": 0, "user_id": null, "create_time": "2024-03-29 14:50:07", "update_time": "2024-03-29 14:50:07", "id": 9, "api_params": [{"name": "query", "description": "搜索关键字（公司名称、公司id、注册号或社会统一信用代码）", "in": "query", "required": true, "schema": {"type": "string"}}]}, {"name": "企业基本信息", "logo": null, "desc": "可以通过公司名称或ID获取企业基本信息，企业基本信息包括公司名称或ID、类型、成立日期、经营状态、注册资本、法人、工商注册号、统一社会信用代码、组织机构代码、纳税人识别号等字段信息", "tool_key": "tianyancha_get_company_baseinfo", "type": 7, "is_preset": 1, "is_delete": 0, "user_id": null, "create_time": "2024-03-29 14:51:06", "update_time": "2024-03-29 14:51:06", "id": 10, "api_params": [{"name": "query", "description": "搜索关键字（公司名称、公司id、注册号或社会统一信用代码）", "in": "query", "required": true, "schema": {"type": "string"}}]}, {"name": "企业知识产权信息", "logo": null, "desc": "可以通过公司名称或ID获取包含商标、专利、作品著作权、软件著作权、网站备案等维度的相关信息", "tool_key": "tianyancha_ip_rights", "type": 7, "is_preset": 1, "is_delete": 0, "user_id": null, "create_time": "2024-03-29 14:51:33", "update_time": "2024-03-29 14:51:33", "id": 11, "api_params": [{"name": "query", "description": "搜索关键字（公司名称、公司id、注册号或社会统一信用代码）", "in": "query", "required": true, "schema": {"type": "string"}}]}, {"name": "企业司法风险", "logo": null, "desc": "可以通过公司名称或ID获取包含法律诉讼、法院公告、开庭公告、失信人、被执行人、立案信息、送达公告等维度的相关信息", "tool_key": "tianyan<PERSON>_judicial_risk", "type": 7, "is_preset": 1, "is_delete": 0, "user_id": null, "create_time": "2024-03-29 14:52:13", "update_time": "2024-03-29 14:52:13", "id": 12, "api_params": [{"name": "query", "description": "搜索关键字（公司名称、公司id、注册号或社会统一信用代码）", "in": "query", "required": true, "schema": {"type": "string"}}]}, {"name": "企业法律诉讼", "logo": null, "desc": "可以通过公司名称或ID获取企业法律诉讼信息，法律诉讼包括案件名称、案由、案件身份、案号等字段的详细信息", "tool_key": "tian<PERSON><PERSON>_law_suit_case", "type": 7, "is_preset": 1, "is_delete": 0, "user_id": null, "create_time": "2024-03-29 14:52:36", "update_time": "2024-03-29 14:52:36", "id": 13, "api_params": [{"name": "query", "description": "搜索关键字（公司名称、公司id、注册号或社会统一信用代码）", "in": "query", "required": true, "schema": {"type": "string"}}]}, {"name": "企业工商信息", "logo": null, "desc": "可以通过公司名称或ID获取包含企业基本信息、主要人员、股东信息、对外投资、分支机构等维度的相关信息", "tool_key": "tianyancha_ic_info", "type": 7, "is_preset": 1, "is_delete": 0, "user_id": null, "create_time": "2024-03-29 14:53:06", "update_time": "2024-03-29 14:53:06", "id": 14, "api_params": [{"name": "query", "description": "搜索关键字（公司名称、公司id、注册号或社会统一信用代码）", "in": "query", "required": true, "schema": {"type": "string"}}]}, {"name": "企业工商信息变更记录", "logo": null, "desc": "可以通过公司名称或ID获取企业变更记录，变更记录包括工商变更事项、变更前后信息等字段的详细信息", "tool_key": "tianyancha_company_change_info", "type": 7, "is_preset": 1, "is_delete": 0, "user_id": null, "create_time": "2024-03-29 14:54:02", "update_time": "2024-03-29 14:54:02", "id": 15, "api_params": [{"name": "query", "description": "搜索关键字（公司名称、公司id、注册号或社会统一信用代码）", "in": "query", "required": true, "schema": {"type": "string"}}]}, {"name": "企业股东", "logo": null, "desc": "可以通过公司名称或ID获取企业股东信息，股东信息包括股东名、出资比例、出资金额、股东总数等字段的详细信息", "tool_key": "tianyancha_company_holders", "type": 7, "is_preset": 1, "is_delete": 0, "user_id": null, "create_time": "2024-03-29 14:54:28", "update_time": "2024-03-29 14:54:28", "id": 16, "api_params": [{"name": "query", "description": "搜索关键字（公司名称、公司id、注册号或社会统一信用代码）", "in": "query", "required": true, "schema": {"type": "string"}}]}, {"name": "企业天眼风险", "logo": null, "desc": "可以通过关键字（公司名称、公司id、注册号或社会统一信用代码）获取企业相关天眼风险列表，包括企业自身/周边/预警风险信息。", "tool_key": "tianyancha_riskinfo", "type": 7, "is_preset": 1, "is_delete": 0, "user_id": null, "create_time": "2024-03-29 14:54:57", "update_time": "2024-03-29 14:54:57", "id": 17, "api_params": [{"name": "query", "description": "搜索关键字（公司名称、公司id、注册号或社会统一信用代码）", "in": "query", "required": true, "schema": {"type": "string"}}]}, {"name": "股票实时行情", "logo": null, "desc": "查询中国A股（沪深北交易所）股票或指数的实时行情数据，返回：现价、涨跌额、涨跌幅、成交量、成交额。", "tool_key": "sina_realtime_info", "type": 8, "extra": null, "is_preset": 1, "is_delete": 0, "user_id": null, "create_time": "2024-03-29 14:56:15", "update_time": "2024-03-29 14:56:15", "id": 18, "api_params": [{"name": "prefix", "description": "前缀。如果是\"stock_symbol\"传入的为股票代码，则需要传入s_;\n如果\"stock_symbol\"传入的为指数代码，则为空。", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "stock_exchange", "description": "交易所简写。股票上市的交易所，或者发布行情指数的交易所。可选项有\"sh\"(上海证券交易所)、\" sz\"( 深圳证券交易所)、\"bj\"( 北京证券交易所)", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "stock_symbol", "description": "6位数字的股票或者指数代码。\\n参考信息：\\n- 如果问题中未给出，可能需要上网查询。\\n- 上交所股票通常以 6 开头，深交所股票通常以 0、3 开头，北交所股票通常以 8 开头。\\n- 上交所行情指数通常以 000 开头，深交所指数通常以 399 开头。同一个指数可能会同时在两个交易所发布，例如沪深 300 有\"sh000300\"和\"sz399300\"两个代码。", "in": "query", "required": true, "schema": {"type": "string"}}]}, {"name": "股票历史行情", "logo": null, "desc": "查询中国A股（沪深北交易所）股票或指数的的历史行情数据，返回：时间、开盘价、最高价、最低价、收盘价、成交量（股）。", "tool_key": "sina_history_KLine", "type": 8, "extra": null, "is_preset": 1, "is_delete": 0, "user_id": null, "create_time": "2024-03-29 14:56:38", "update_time": "2024-03-29 14:56:38", "id": 19, "api_params": [{"name": "date", "description": "需要查询的时间，按照”2024-03-26“格式，传入日期", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "stock_exchange", "description": "交易所简写。股票上市的交易所，或者发布行情指数的交易所。可选项有\"sh\"(上海证券交易所)、\" sz\"( 深圳证券交易所)、\"bj\"( 北京证券交易所)", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "stock_symbol", "description": "6位数字的股票或者指数代码。\\n参考信息：\\n- 如果问题中未给出，可能需要上网查询。\\n- 上交所股票通常以 6 开头，深交所股票通常以 0、3 开头，北交所股票通常以 8 开头。\\n- 上交所行情指数通常以 000 开头，深交所指数通常以 399 开头。同一个指数可能会同时在两个交易所发布，例如沪深 300 有\"sh000300\"和\"sz399300\"两个代码。", "in": "query", "required": true, "schema": {"type": "string"}}]}, {"name": "社会融资规模增量", "logo": null, "desc": "中国社会融资规模增量月度统计数据。返回数据包括：月份，社会融资规模增量(单位：亿元)，社融增量分项数据。分项数据具体包括：人民币贷款，外币贷款，委托贷款，信托贷款，未贴现银行承兑汇票，企业债券，非金融企业境内股票融资。", "tool_key": "macro_china_shrzgm", "type": 8, "extra": null, "is_preset": 1, "is_delete": 0, "user_id": null, "create_time": "2024-03-29 14:59:06", "update_time": "2024-03-29 14:59:06", "id": 20, "api_params": [{"name": "start_date", "description": "开始月份, 使用YYYY-MM-DD 方式表示", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "end_date", "description": "结束月份, 使用YYYY-MM-DD 方式表示", "in": "query", "required": true, "schema": {"type": "string"}}]}, {"name": "GDP", "logo": null, "desc": " 中国国内生产总值（GDP）季度统计数据。返回数据包括：季度，当年累计GDP 绝对值及同比增长情况，第一、二、三产业 GDP 绝对值以及同比增长情况。", "tool_key": "macro_china_gdp_yearly", "type": 8, "extra": null, "is_preset": 1, "is_delete": 0, "user_id": null, "create_time": "2024-03-29 14:59:38", "update_time": "2024-03-29 14:59:38", "id": 21, "api_params": [{"name": "start_date", "description": "开始月份, 使用YYYY-MM-DD 方式表示", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "end_date", "description": "结束月份, 使用YYYY-MM-DD 方式表示", "in": "query", "required": true, "schema": {"type": "string"}}]}, {"name": "CPI", "logo": null, "desc": "中国居民消费价格指数(CPI，上年同月=100)月度统计数据。返回数据包括：月份，全国当月 CPI，全国当月同比增长，全国当月环比增长，全国当年 CPI 累计值；城市当月 CPI，城市当月同比增长，城市当月环比增长，城市当年 CPI 累计值；农村当月 CPI，农村当月同比增长，农村当月环比增长，农村当年 CPI 累计值。", "tool_key": "macro_china_cpi", "type": 8, "extra": null, "is_preset": 1, "is_delete": 0, "user_id": null, "create_time": "2024-03-29 15:00:00", "update_time": "2024-03-29 15:00:00", "id": 22, "api_params": [{"name": "start_date", "description": "开始月份, 使用YYYY-MM-DD 方式表示", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "end_date", "description": "结束月份, 使用YYYY-MM-DD 方式表示", "in": "query", "required": true, "schema": {"type": "string"}}]}, {"name": "PPI", "logo": null, "desc": "中国工业品出厂价格指数（PPI）月度统计数据。返回数据包括：月份，当月 PPI，当月同比增长，当年 PPI 累计值。", "tool_key": "macro_china_ppi", "type": 8, "extra": null, "is_preset": 1, "is_delete": 0, "user_id": null, "create_time": "2024-03-29 15:00:24", "update_time": "2024-03-29 15:00:24", "id": 23, "api_params": [{"name": "start_date", "description": "开始月份, 使用YYYY-MM-DD 方式表示", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "end_date", "description": "结束月份, 使用YYYY-MM-DD 方式表示", "in": "query", "required": true, "schema": {"type": "string"}}]}, {"name": "货币供应量", "logo": null, "desc": "中国货币供应量月度统计数据。返回数据包括：月份，当月 M0、M1、M2数量以及同、环比情况。", "tool_key": "macro_china_money_supply", "type": 8, "extra": null, "is_preset": 1, "is_delete": 0, "user_id": null, "create_time": "2024-03-29 15:00:54", "update_time": "2024-03-29 15:00:54", "id": 24, "api_params": [{"name": "start_date", "description": "开始月份, 使用YYYY-MM-DD 方式表示", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "end_date", "description": "结束月份, 使用YYYY-MM-DD 方式表示", "in": "query", "required": true, "schema": {"type": "string"}}]}, {"name": "社会消费品零售总额", "logo": null, "desc": "中国社会消费品零售总额月度统计数据。返回数据包括：月份，当月总额以及同、环比，当年累计总额以及同比情况。", "tool_key": "macro_china_consumer_goods_retail", "type": 8, "extra": null, "is_preset": 1, "is_delete": 0, "user_id": null, "create_time": "2024-03-29 15:01:47", "update_time": "2024-03-29 15:01:47", "id": 25, "api_params": [{"name": "start_date", "description": "开始月份, 使用YYYY-MM-DD 方式表示", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "end_date", "description": "结束月份, 使用YYYY-MM-DD 方式表示", "in": "query", "required": true, "schema": {"type": "string"}}]}, {"name": "PMI", "logo": null, "desc": "中国 PMI （采购经理人指数）月度统计数据。返回数据包括：月份制造业 PMI，制造业 PMI 同比增长，非制造业 PMI，非制造业 PMI 同比增长。", "tool_key": "macro_china_pmi", "type": 8, "extra": null, "is_preset": 1, "is_delete": 0, "user_id": null, "create_time": "2024-04-15 12:01:11", "update_time": "2024-04-15 12:01:11", "id": 26, "api_params": [{"name": "start_date", "description": "开始月份, 使用YYYY-MM-DD 方式表示", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "end_date", "description": "结束月份, 使用YYYY-MM-DD 方式表示", "in": "query", "required": true, "schema": {"type": "string"}}]}, {"name": "中美国债收益率", "logo": null, "desc": "本接口返回指定时间段[start_date,end_date]内交易日的中美两国的 2 年、5 年、10 年、30 年、10 年-2 年收益率数据。start_date表示起始日期，end_date表示结束日期，日期格式例如 2024-04-07", "tool_key": "macro_bond_zh_us_rate", "type": 8, "extra": null, "is_preset": 1, "is_delete": 0, "user_id": null, "create_time": "2024-04-15 12:03:27", "update_time": "2024-04-15 12:03:27", "id": 27, "api_params": [{"name": "start_date", "description": "开始月份, 使用YYYY-MM-DD 方式表示", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "end_date", "description": "结束月份, 使用YYYY-MM-DD 方式表示", "in": "query", "required": true, "schema": {"type": "string"}}]}, {"name": "发送钉钉群消息", "logo": null, "desc": "通过钉钉群机器人，快速将消息推送到指定钉钉群组中", "api_params": [{"in": "query", "name": "url", "schema": {"type": "string"}, "required": true, "description": "自定义机器人的Wehhook地址"}, {"in": "query", "name": "message", "schema": {"type": "string"}, "required": true, "description": "发送的文本消息内容"}], "tool_key": "ding_send_message", "type": 9, "extra": null, "is_preset": 1, "is_delete": 0, "user_id": 3, "create_time": "2024-05-08 14:36:50", "update_time": "2024-05-08 14:36:50", "id": 28}, {"name": "单页面爬取|对应 Scrape 模式", "logo": null, "desc": "爬取并返回指定 URL 页面的内容，不会爬取子页面。", "api_params": [{"in": "path", "name": "target_url", "schema": {"type": "string"}, "required": true, "description": "要爬取的网站 url"}], "tool_key": "fire_search_scrape", "type": 10, "extra": null, "is_preset": 1, "is_delete": 0, "user_id": 3, "create_time": "2024-05-08 14:36:50", "update_time": "2025-02-27 19:05:31", "id": 29}, {"name": "获取单网页", "logo": null, "desc": "爬取指定URL（支持pdf），并将其转换为适合大模型处理的markdown格式", "api_params": [{"in": "path", "name": "target_url", "schema": {"type": "string"}, "required": true, "description": "要获取的目标网页"}], "tool_key": "jina_get_markdown", "type": 11, "is_preset": 1, "is_delete": 0, "user_id": 3, "create_time": "2024-05-08 14:36:50", "update_time": "2025-02-14 15:06:45", "id": 30}, {"name": "Stable diffusion", "logo": null, "desc": "使用Stable Diffusion模型，根据用户提示词生成图像", "api_params": [{"in": "path", "name": "prompt", "schema": {"type": "string"}, "required": true, "description": "提示词生成图片描述词"}, {"in": "path", "name": "negative_prompt", "schema": {"type": "string"}, "required": false, "description": "不希望图片包含的内容"}], "tool_key": "silicon_stable_diffusion", "type": 12, "is_preset": 1, "is_delete": 0, "user_id": 3, "create_time": "2024-05-08 14:36:50", "update_time": "2025-02-20 11:47:25", "id": 31}, {"name": "Flux", "logo": null, "desc": "使用Flux模型，根据用户提示词生成图像", "api_params": [{"in": "path", "name": "prompt", "schema": {"type": "string"}, "required": true, "description": "提示词生成图片描述词（建议使用英文）"}], "tool_key": "silicon_flux", "type": 12, "is_preset": 1, "is_delete": 0, "user_id": 3, "create_time": "2024-05-08 14:36:50", "update_time": "2025-02-20 11:47:25", "id": 32}, {"name": "向指定用户或者群聊发送消息", "logo": null, "desc": "向指定用户或者群聊发送消息", "api_params": [{"in": "query", "name": "message", "schema": {"type": "string"}, "required": true, "description": "发送的文本消息内容"}, {"in": "query", "name": "receive_id", "schema": {"type": "string"}, "required": true, "description": "消息接收者的id"}, {"in": "query", "name": "receive_id_type", "schema": {"type": "string"}, "required": true, "description": "用户id类型，可选值：open_id（标识一个用户在某个应用中的身份）；union_id（标识一个用户在某个应用开发商下的身份）；user_id（标识一个用户在某个租户内的身份）；email（以用户的真实邮箱来标识用户）；chat_id（以群 ID 来标识群聊）"}], "tool_key": "feishu_send_message", "type": 13, "is_preset": 1, "is_delete": 0, "user_id": 3, "create_time": "2024-05-08 14:36:50", "update_time": "2025-02-24 14:35:04", "id": 33}, {"name": "发送企业微信群消息", "logo": null, "desc": "通过企业微信群机器人，快速将消息推送到指定企业微信群组中", "api_params": [{"in": "query", "name": "url", "schema": {"type": "string"}, "required": true, "description": "微信机器人的webhook地址"}, {"in": "query", "name": "message", "schema": {"type": "string"}, "required": true, "description": "发送的消息内容"}], "tool_key": "wechat_send_message", "type": 15, "extra": "{}", "is_preset": 1, "is_delete": 0, "user_id": 3, "create_time": "2024-05-08 14:36:50", "update_time": "2025-02-08 10:55:32", "id": 34}, {"name": "发送邮件", "logo": null, "desc": "给单个或多个邮箱发送邮件（多个邮箱账号使用\",\"分隔）", "api_params": [{"in": "query", "name": "receiver", "schema": {"type": "string"}, "required": true, "description": "接受邮件的邮箱地址，确定邮件发送对象"}, {"in": "query", "name": "subject", "schema": {"type": "string"}, "required": false, "description": "邮件主题"}, {"in": "query", "name": "content", "schema": {"type": "string"}, "required": true, "description": "邮件的正文内容"}], "tool_key": "email_send_email", "type": 14, "is_preset": 1, "is_delete": 0, "user_id": 3, "create_time": "2024-05-08 14:36:50", "update_time": "2025-03-03 22:10:20", "id": 35}, {"name": "深度爬取|对应 Crawl 模式", "logo": null, "desc": "爬取并返回指定 URL 以及所有可访问子页面的内容。", "api_params": [{"in": "path", "name": "target_url", "schema": {"type": "string"}, "required": true, "description": "要爬取网站的起始 url。"}], "tool_key": "fire_search_crawl", "type": 10, "is_preset": 1, "is_delete": 0, "user_id": 3, "create_time": "2024-05-08 14:36:50", "update_time": "2025-02-27 19:05:31", "id": 36}, {"name": "获取指定单聊、群聊的历史消息", "logo": null, "desc": "支持在单聊或群聊中快速获取相关内容", "api_params": [{"in": "query", "name": "container_id", "schema": {"type": "string"}, "required": true, "description": "单聊或群聊的id，或话题 id"}, {"in": "query", "name": "container_id_type", "schema": {"type": "string"}, "required": true, "description": "容器类型。 可选值有： chat：包含单聊（p2p）和群聊（group）； thread：话题 。"}, {"in": "query", "name": "start_time", "schema": {"type": "string"}, "required": false, "description": "待查询历史信息的起始时间，秒级时间戳。 注意：thread 容器类型暂不支持获取指定时间范围内的消息。"}, {"in": "query", "name": "end_time", "schema": {"type": "string"}, "required": false, "description": "待查询历史信息的结束时间，秒级时间戳。注意：thread 容器类型暂不支持获取指定时间范围内的消息。"}, {"in": "query", "name": "page_size", "schema": {"type": "string"}, "required": false, "description": "分页大小，单次请求所返回的数据条目数，默认值20，取值范围1~50。"}, {"in": "query", "name": "sort_type", "schema": {"type": "string"}, "required": false, "description": "可选值有：ByCreateTimeAsc（按消息创建时间升序排列）；ByCreateTimeDesc（按消息创建时间降序排列）"}, {"in": "query", "name": "page_token", "schema": {"type": "string"}, "required": false, "description": "分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果"}], "tool_key": "feishu_get_chat_messages", "type": 13, "is_preset": 1, "is_delete": 0, "user_id": 3, "create_time": "2024-05-08 14:36:50", "update_time": "2025-02-24 14:35:04", "id": 37}, {"name": "联网搜索", "logo": null, "desc": "使用 query 进行联网检索并返回结果。", "api_params": [{"name": "query", "description": "Search query", "in": "query", "required": true, "schema": {"type": "string"}}], "tool_key": "web_search", "type": 16, "is_preset": 1, "is_delete": 0, "user_id": 1, "id": 38}]