[{"id": 1, "name": "时间", "logo": null, "description": "获取当前时间", "auth_method": null, "api_key": null, "auth_type": null, "is_preset": 1, "user_id": null, "is_delete": 0, "create_time": "2024-04-30 10:25:26", "update_time": "2024-04-30 10:25:26", "openapi_schema": null}, {"id": 2, "name": "计算器", "logo": null, "description": "使用 Python 内置工具进行数学表达式计算", "auth_method": null, "api_key": null, "auth_type": null, "is_preset": 1, "user_id": null, "is_delete": 0, "create_time": "2024-04-30 10:28:30", "update_time": "2024-04-30 10:28:30", "openapi_schema": null}, {"id": 3, "name": "论文获取", "logo": null, "description": "从 Arxiv 网站检索论文的工具，输入为检索关键词。", "auth_method": null, "api_key": null, "auth_type": null, "is_preset": 1, "user_id": null, "is_delete": 0, "create_time": "2024-04-30 10:28:49", "update_time": "2024-04-30 10:28:49", "openapi_schema": null}, {"id": 4, "name": "Dalle3绘画", "logo": "", "description": "OpenAI 文生图模型", "auth_method": null, "api_key": null, "auth_type": null, "is_preset": 1, "user_id": null, "is_delete": 0, "create_time": "2024-04-30 10:24:30", "update_time": "2024-04-30 10:24:30", "openapi_schema": null}, {"id": 5, "name": "Bing web搜索", "logo": "", "description": "BIng 搜索引擎，可联网检索互联网信息，例如天气、汇率、时事等", "auth_method": null, "api_key": null, "auth_type": null, "is_preset": 1, "user_id": null, "is_delete": 0, "create_time": "2024-04-30 10:23:20", "update_time": "2024-04-30 10:23:20", "openapi_schema": null}, {"id": 6, "name": "代码执行器", "logo": null, "description": "通过执行代码完成图表绘制、文件处理等编程类操作", "auth_method": null, "api_key": null, "auth_type": null, "is_preset": 1, "user_id": null, "is_delete": 0, "create_time": "2024-04-30 10:26:07", "update_time": "2024-04-30 10:26:07", "openapi_schema": null}, {"id": 7, "name": "天眼查", "logo": null, "description": "企业信息查询", "auth_method": null, "api_key": null, "auth_type": null, "is_preset": 1, "user_id": null, "is_delete": 0, "create_time": "2024-04-30 10:26:40", "update_time": "2024-04-30 10:26:40", "openapi_schema": null}, {"id": 8, "name": "经济金融数据", "logo": null, "description": "包含股票、基金、期货等行情数据和宏观经济、公司财务等基本面数据的金融大数据平台", "auth_method": null, "api_key": null, "auth_type": null, "is_preset": 1, "user_id": null, "is_delete": 0, "create_time": "2024-04-30 10:27:12", "update_time": "2024-04-30 10:27:12", "openapi_schema": null}, {"id": 9, "name": "钉钉", "logo": "", "description": "钉钉群机器人发送消息工具", "server_host": "", "auth_method": 0, "api_key": "", "auth_type": "basic", "is_preset": 1, "user_id": null, "is_delete": 0, "create_time": "2024-04-30 10:26:40", "update_time": "2025-01-10 21:22:37", "openapi_schema": null}, {"id": 10, "name": "Firecrawl", "logo": "", "description": "指定 URL 爬取网页内容，并将其转换为 Markdown 格式", "server_host": "", "auth_method": 0, "api_key": "", "auth_type": "basic", "is_preset": 1, "user_id": null, "is_delete": 0, "create_time": "2024-04-30 10:26:40", "update_time": "2025-02-27 19:05:31", "openapi_schema": null}, {"id": 11, "name": "Jina AI", "logo": "", "description": "将目标网址（支持 PDF）内容转换为大模型可处理的 Markdown 格式", "server_host": "", "auth_method": 0, "api_key": "", "auth_type": "basic", "is_preset": 1, "user_id": null, "is_delete": 0, "create_time": "2024-04-30 10:26:40", "update_time": "2025-02-14 15:06:45", "openapi_schema": null}, {"id": 12, "name": "SiliconFlow", "logo": "", "description": "基于文本提示生成高质量图像，支持 Flux 和 Stable Diffusion 模型", "server_host": "", "auth_method": 0, "api_key": "", "auth_type": "basic", "is_preset": 1, "user_id": null, "is_delete": 0, "create_time": "2024-04-30 10:26:40", "update_time": "2025-02-20 11:47:25", "openapi_schema": null}, {"id": 13, "name": "飞书消息", "logo": "", "description": "支持获取飞书单聊或群聊历史消息，向指定用户或者群聊发送消息", "server_host": "", "auth_method": 0, "api_key": "", "auth_type": "basic", "is_preset": 1, "user_id": null, "is_delete": 0, "create_time": "2024-04-30 10:26:40", "update_time": "2025-02-24 14:35:04", "openapi_schema": null}, {"id": 14, "name": "发送邮件", "logo": "", "description": "通过smtp协议发送电子邮件", "server_host": "", "auth_method": 0, "api_key": "", "auth_type": "basic", "is_preset": 1, "user_id": null, "is_delete": 0, "create_time": "2024-04-30 10:26:40", "update_time": "2025-03-03 22:10:20", "openapi_schema": null}, {"id": 15, "name": "企业微信", "logo": "", "description": "企业微信机器人发送群消息工具", "server_host": "", "auth_method": 0, "api_key": "", "auth_type": "basic", "is_preset": 1, "user_id": null, "is_delete": 0, "create_time": "2024-04-30 10:26:40", "update_time": "2025-01-10 21:22:37", "openapi_schema": null}, {"id": 16, "name": "联网搜索", "logo": "", "description": "搜索互联网信息，可配置使用不同的搜索引擎，目前支持 Bing、博查、Jina 深度搜索、SerpApi、Tavily。", "server_host": "", "auth_method": 0, "api_key": "", "auth_type": "basic", "is_preset": 1, "user_id": null, "is_delete": 0, "openapi_schema": null}]