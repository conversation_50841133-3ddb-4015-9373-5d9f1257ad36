[{"单文档问答": {"id": "RetrievalQA-3568b", "type": "InputFileNode", "node": {"output_types": [], "display_name": "单文档问答", "documentation": "", "base_classes": ["BaseRetrievalQA", "RetrievalQA", "Chain", "function"], "description": "Chain for question-answering against an index.", "template": {"token_max_CombineDocsChain-Pud2p": {"l2": false, "info": "当前只对stuff 生效", "list": false, "name": "token_max", "show": true, "type": "int", "value": -1, "l2_name": "token_max", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "token_max", "proxy": {"id": "CombineDocsChain-Pud2p", "field": "token_max"}}, "chain_type_CombineDocsChain-Pud2p": {"l2": false, "info": "", "list": true, "name": "chain_type", "show": true, "type": "str", "value": "stuff", "l2_name": "chain_type", "options": ["stuff", "map_reduce", "map_rerank", "refine"], "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "proxy": {"id": "CombineDocsChain-Pud2p", "field": "chain_type"}, "display_name": "Chain Type"}, "tags_RetrievalQA-qH6Mk": {"l2": false, "info": "", "list": true, "name": "tags", "show": false, "type": "str", "l2_name": "tags", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-qH6Mk", "field": "tags"}, "display_name": "Tags"}, "memory_RetrievalQA-qH6Mk": {"l2": false, "info": "", "list": false, "name": "memory", "show": true, "type": "BaseMemory", "l2_name": "memory", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-qH6Mk", "field": "memory"}, "display_name": "Memory"}, "verbose_RetrievalQA-qH6Mk": {"l2": false, "info": "", "list": false, "name": "verbose", "show": false, "type": "bool", "l2_name": "verbose", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-qH6Mk", "field": "verbose"}, "display_name": "Verbose"}, "metadata_RetrievalQA-qH6Mk": {"l2": false, "info": "", "list": false, "name": "metadata", "show": false, "type": "code", "l2_name": "metadata", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-qH6Mk", "field": "metadata"}, "display_name": "<PERSON><PERSON><PERSON>"}, "callbacks_RetrievalQA-qH6Mk": {"l2": false, "info": "", "list": true, "name": "callbacks", "show": false, "type": "langchain.callbacks.base.BaseCallbackHandler", "l2_name": "callbacks", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-qH6Mk", "field": "callbacks"}, "display_name": "Callbacks"}, "input_key_RetrievalQA-qH6Mk": {"l2": false, "info": "", "list": false, "name": "input_key", "show": true, "type": "str", "value": "query", "l2_name": "input_key", "advanced": true, "password": false, "required": true, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-qH6Mk", "field": "input_key"}, "display_name": "Input Key"}, "input_node_RetrievalQA-qH6Mk": {"l2": false, "info": "", "list": false, "name": "input_node", "show": true, "type": "input", "l2_name": "input_node", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Preset Question", "proxy": {"id": "RetrievalQA-qH6Mk", "field": "input_node"}}, "output_key_RetrievalQA-qH6Mk": {"l2": false, "info": "", "list": false, "name": "output_key", "show": true, "type": "str", "value": "result", "l2_name": "output_key", "advanced": true, "password": false, "required": true, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-qH6Mk", "field": "output_key"}, "display_name": "Output Key"}, "return_source_documents_RetrievalQA-qH6Mk": {"l2": false, "info": "", "list": false, "name": "return_source_documents", "show": true, "type": "bool", "value": true, "l2_name": "return_source_documents", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-qH6Mk", "field": "return_source_documents"}, "display_name": "Return Source Documents"}, "proxy_url_OpenAIProxyEmbedding-yvld7": {"l2": false, "info": "", "list": false, "name": "proxy_url", "show": false, "type": "str", "value": "", "l2_name": "proxy_url", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "http://*************:8080", "proxy": {"id": "OpenAIProxyEmbedding-yvld7", "field": "proxy_url"}, "display_name": "Proxy Url"}, "file_path_InputFileNode-jdLkB": {"l2": false, "info": "", "list": false, "name": "file_path", "show": true, "type": "file", "value": "", "l2_name": "file_path", "advanced": true, "password": false, "required": false, "suffixes": [".html", ".md", ".txt", ".jpg", ".png", ".jpeg", ".csv", ".doc", ".docx", ".pdf", ".ppt", ".pptx", ".xlsx", ".tiff"], "fileTypes": ["html", "md", "txt", "jpg", "png", "jpeg", "csv", "doc", "docx", "pdf", "ppt", "pptx", "tiff", "xlsx"], "file_path": null, "multiline": false, "placeholder": "", "display_name": "输入内容", "proxy": {"id": "InputFileNode-jdLkB", "field": "file_path"}}, "n_ProxyChatLLM-rnE4s": {"l2": false, "info": "", "list": false, "name": "n", "show": false, "type": "int", "value": 1, "l2_name": "n", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-rnE4s", "field": "n"}, "display_name": "N"}, "tags_ProxyChatLLM-rnE4s": {"l2": false, "info": "", "list": true, "name": "tags", "show": false, "type": "str", "l2_name": "tags", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-rnE4s", "field": "tags"}, "display_name": "Tags"}, "cache_ProxyChatLLM-rnE4s": {"l2": false, "info": "", "list": false, "name": "cache", "show": false, "type": "bool", "l2_name": "cache", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-rnE4s", "field": "cache"}, "display_name": "<PERSON><PERSON>"}, "top_p_ProxyChatLLM-rnE4s": {"l2": false, "info": "", "list": false, "name": "top_p", "show": true, "type": "float", "value": 0.9, "l2_name": "top_p", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-rnE4s", "field": "top_p"}, "display_name": "Top P"}, "client_ProxyChatLLM-rnE4s": {"l2": false, "info": "", "list": false, "name": "client", "show": false, "type": "Any", "l2_name": "client", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-rnE4s", "field": "client"}, "display_name": "Client"}, "headers_ProxyChatLLM-rnE4s": {"l2": false, "info": "", "list": false, "name": "headers", "show": false, "type": "code", "value": "", "l2_name": "headers", "advanced": true, "password": false, "required": false, "multiline": true, "placeholder": "", "proxy": {"id": "ProxyChatLLM-rnE4s", "field": "headers"}, "display_name": "Headers"}, "verbose_ProxyChatLLM-rnE4s": {"l2": false, "info": "", "list": false, "name": "verbose", "show": false, "type": "bool", "value": false, "l2_name": "verbose", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-rnE4s", "field": "verbose"}, "display_name": "Verbose"}, "metadata_ProxyChatLLM-rnE4s": {"l2": false, "info": "", "list": false, "name": "metadata", "show": false, "type": "code", "l2_name": "metadata", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-rnE4s", "field": "metadata"}, "display_name": "<PERSON><PERSON><PERSON>"}, "callbacks_ProxyChatLLM-rnE4s": {"l2": false, "info": "", "list": true, "name": "callbacks", "show": false, "type": "langchain.callbacks.base.BaseCallbackHandler", "l2_name": "callbacks", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-rnE4s", "field": "callbacks"}, "display_name": "Callbacks"}, "streaming_ProxyChatLLM-rnE4s": {"l2": false, "info": "", "list": false, "name": "streaming", "show": false, "type": "bool", "value": false, "l2_name": "streaming", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-rnE4s", "field": "streaming"}, "display_name": "Streaming"}, "max_tokens_ProxyChatLLM-rnE4s": {"l2": false, "info": "", "list": false, "name": "max_tokens", "show": true, "type": "int", "value": "", "l2_name": "max_tokens", "advanced": true, "password": true, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-rnE4s", "field": "max_tokens"}, "display_name": "<PERSON>"}, "model_name_ProxyChatLLM-rnE4s": {"l2": false, "info": "", "list": false, "name": "model_name", "show": true, "type": "str", "value": "gpt-3.5-turbo-16k-0613", "l2_name": "model_name", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-rnE4s", "field": "model_name"}, "display_name": "Model Name"}, "max_retries_ProxyChatLLM-rnE4s": {"l2": false, "info": "", "list": false, "name": "max_retries", "show": false, "type": "int", "value": 6, "l2_name": "max_retries", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-rnE4s", "field": "max_retries"}, "display_name": "Max Retries"}, "temperature_ProxyChatLLM-rnE4s": {"l2": false, "info": "", "list": false, "name": "temperature", "show": true, "type": "float", "value": 0.7, "l2_name": "temperature", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-rnE4s", "field": "temperature"}, "display_name": "Temperature"}, "model_kwargs_ProxyChatLLM-rnE4s": {"l2": false, "info": "", "list": false, "name": "model_kwargs", "show": false, "type": "code", "l2_name": "model_kwargs", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-rnE4s", "field": "model_kwargs"}, "display_name": "Model Kwargs"}, "elemai_api_key_ProxyChatLLM-rnE4s": {"l2": false, "info": "", "list": false, "name": "elemai_api_key", "show": true, "type": "str", "value": "", "l2_name": "elemai_api_key", "advanced": true, "password": true, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-rnE4s", "field": "elemai_api_key"}, "display_name": "Elemai Api Key"}, "elemai_base_url_ProxyChatLLM-rnE4s": {"l2": false, "info": "", "list": false, "name": "elemai_base_url", "show": true, "type": "str", "value": "http://192.168.106.12:6001/v1/chatcompletion", "l2_name": "elemai_base_url", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-rnE4s", "field": "elemai_base_url"}, "display_name": "Elemai Base Url"}, "request_timeout_ProxyChatLLM-rnE4s": {"l2": false, "info": "", "list": false, "name": "request_timeout", "show": false, "type": "float", "l2_name": "request_timeout", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-rnE4s", "field": "request_timeout"}, "display_name": "Request Timeout"}, "tiktoken_model_name_ProxyChatLLM-rnE4s": {"l2": false, "info": "", "list": false, "name": "tiktoken_model_name", "show": false, "type": "str", "l2_name": "tiktoken_model_name", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-rnE4s", "field": "tiktoken_model_name"}, "display_name": "Tiktoken Model Name"}, "drop_old_Milvus-T3kRH": {"l2": false, "info": "", "list": false, "name": "drop_old", "show": false, "type": "bool", "value": false, "l2_name": "drop_old", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-T3kRH", "field": "drop_old"}, "display_name": "Drop Old"}, "metadatas_Milvus-T3kRH": {"l2": false, "info": "", "list": true, "name": "metadatas", "show": false, "type": "code", "l2_name": "metadatas", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-T3kRH", "field": "metadatas"}, "display_name": "Metadatas"}, "index_params_Milvus-T3kRH": {"l2": false, "info": "", "list": false, "name": "index_params", "show": false, "type": "code", "l2_name": "index_params", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-T3kRH", "field": "index_params"}, "display_name": "Index Params"}, "search_kwargs_Milvus-T3kRH": {"l2": false, "info": "", "list": false, "name": "search_kwargs", "show": false, "type": "code", "value": "{}", "l2_name": "search_kwargs", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-T3kRH", "field": "search_kwargs"}, "display_name": "Search Kwargs"}, "search_params_Milvus-T3kRH": {"l2": false, "info": "", "list": false, "name": "search_params", "show": false, "type": "code", "l2_name": "search_params", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-T3kRH", "field": "search_params"}, "display_name": "Search Params"}, "collection_name_Milvus-T3kRH": {"l2": false, "info": "", "list": false, "name": "collection_name", "show": true, "type": "str", "value": "", "l2_name": "collection_name", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "collection_id": "", "proxy": {"id": "Milvus-T3kRH", "field": "collection_name"}, "display_name": "Collection Name"}, "connection_args_Milvus-T3kRH": {"l2": false, "info": "", "list": false, "name": "connection_args", "show": false, "type": "code", "value": "", "l2_name": "connection_args", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-T3kRH", "field": "connection_args"}, "display_name": "Connection Args"}, "consistency_level_Milvus-T3kRH": {"l2": false, "info": "", "list": false, "name": "consistency_level", "show": false, "type": "str", "value": "Session", "l2_name": "consistency_level", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-T3kRH", "field": "consistency_level"}, "display_name": "Consistency Level"}, "metadata_ElemUnstructuredLoaderV0-LAGXM": {"info": "", "list": false, "name": "metadata", "show": false, "type": "code", "value": "{}", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "display_name": "<PERSON><PERSON><PERSON>", "proxy": {"id": "ElemUnstructuredLoaderV0-LAGXM", "field": "metadata"}}, "unstructured_api_url_ElemUnstructuredLoaderV0-LAGXM": {"info": "", "list": false, "name": "unstructured_api_url", "show": true, "type": "str", "value": "", "advanced": false, "password": true, "required": true, "multiline": false, "placeholder": "", "display_name": "unstructured_api_url", "proxy": {"id": "ElemUnstructuredLoaderV0-LAGXM", "field": "unstructured_api_url"}}, "chunk_size_RecursiveCharacterTextSplitter-a7fc9": {"info": "", "list": false, "name": "chunk_size", "show": true, "type": "int", "value": 1000, "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "display_name": "Chunk Size", "proxy": {"id": "RecursiveCharacterTextSplitter-a7fc9", "field": "chunk_size"}}, "separators_RecursiveCharacterTextSplitter-a7fc9": {"info": "", "list": false, "name": "separators", "show": true, "type": "str", "value": "\\n\\n", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "display_name": "Separator", "proxy": {"id": "RecursiveCharacterTextSplitter-a7fc9", "field": "separators"}}, "chunk_overlap_RecursiveCharacterTextSplitter-a7fc9": {"info": "", "list": false, "name": "chunk_overlap", "show": true, "type": "int", "value": 200, "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "display_name": "<PERSON><PERSON>", "proxy": {"id": "RecursiveCharacterTextSplitter-a7fc9", "field": "chunk_overlap"}}, "separator_type_RecursiveCharacterTextSplitter-a7fc9": {"info": "", "list": true, "name": "separator_type", "show": true, "type": "str", "value": "Text", "options": ["Text", "cobol", "cpp", "csharp", "go", "html", "java", "js", "kotlin", "latex", "markdown", "php", "proto", "python", "rst", "ruby", "rust", "scala", "sol", "swift", "ts"], "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "display_name": "Separator Type", "proxy": {"id": "RecursiveCharacterTextSplitter-a7fc9", "field": "separator_type"}}}, "flow": {"data": {"nodes": [{"width": 384, "height": 376, "id": "CombineDocsChain-Pud2p", "type": "genericNode", "position": {"x": 1222.7195691813163, "y": -12.131560981144517}, "data": {"id": "CombineDocsChain-Pud2p", "node": {"template": {"llm": {"l2": false, "info": "", "list": false, "name": "llm", "show": true, "type": "BaseLanguageModel", "l2_name": "llm", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "display_name": "LLM"}, "_type": "load_qa_chain", "token_max": {"l2": false, "info": "当前只对stuff 生效", "list": false, "name": "token_max", "show": true, "type": "int", "value": -1, "l2_name": "token_max", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "token_max"}, "chain_type": {"l2": false, "info": "", "list": true, "name": "chain_type", "show": true, "type": "str", "value": "stuff", "l2_name": "chain_type", "options": ["stuff", "map_reduce", "map_rerank", "refine"], "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}}, "description": "Load question answering chain.", "base_classes": ["BaseCombineDocumentsChain", "function"], "display_name": "CombineDocsChain", "output_types": [], "custom_fields": {}, "documentation": ""}, "type": "CombineDocsChain", "value": null}, "selected": true, "positionAbsolute": {"x": 1222.7195691813163, "y": -12.131560981144517}}, {"width": 384, "height": 316, "id": "RetrievalQA-qH6Mk", "type": "genericNode", "position": {"x": 1898.9908793385102, "y": 150.74108594665665}, "data": {"id": "RetrievalQA-qH6Mk", "node": {"template": {"tags": {"l2": false, "info": "", "list": true, "name": "tags", "show": false, "type": "str", "l2_name": "tags", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "_type": "RetrievalQA", "memory": {"l2": false, "info": "", "list": false, "name": "memory", "show": true, "type": "BaseMemory", "l2_name": "memory", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "verbose": {"l2": false, "info": "", "list": false, "name": "verbose", "show": false, "type": "bool", "l2_name": "verbose", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "metadata": {"l2": false, "info": "", "list": false, "name": "metadata", "show": false, "type": "code", "l2_name": "metadata", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "callbacks": {"l2": false, "info": "", "list": true, "name": "callbacks", "show": false, "type": "langchain.callbacks.base.BaseCallbackHandler", "l2_name": "callbacks", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "input_key": {"l2": false, "info": "", "list": false, "name": "input_key", "show": true, "type": "str", "value": "query", "l2_name": "input_key", "advanced": true, "password": false, "required": true, "multiline": false, "placeholder": ""}, "retriever": {"l2": false, "info": "", "list": false, "name": "retriever", "show": true, "type": "BaseRetriever", "l2_name": "retriever", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}, "input_node": {"l2": false, "info": "", "list": false, "name": "input_node", "show": true, "type": "input", "l2_name": "input_node", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Preset Question"}, "output_key": {"l2": false, "info": "", "list": false, "name": "output_key", "show": true, "type": "str", "value": "result", "l2_name": "output_key", "advanced": true, "password": false, "required": true, "multiline": false, "placeholder": ""}, "combine_documents_chain": {"l2": false, "info": "", "list": false, "name": "combine_documents_chain", "show": true, "type": "BaseCombineDocumentsChain", "l2_name": "combine_documents_chain", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}, "return_source_documents": {"l2": false, "info": "", "list": false, "name": "return_source_documents", "show": true, "type": "bool", "value": true, "l2_name": "return_source_documents", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}}, "description": "Chain for question-answering against an index.", "base_classes": ["BaseRetrievalQA", "RetrievalQA", "Chain", "function"], "display_name": "RetrievalQA", "output_types": [], "custom_fields": {}, "documentation": "https://python.langchain.com/docs/modules/chains/popular/vector_db_qa"}, "type": "RetrievalQA", "value": null}, "selected": true, "positionAbsolute": {"x": 1898.9908793385102, "y": 150.74108594665665}}, {"width": 384, "height": 172, "id": "OpenAIProxyEmbedding-yvld7", "type": "genericNode", "position": {"x": 585.3522336200281, "y": 1118.8504631949986}, "data": {"id": "OpenAIProxyEmbedding-yvld7", "node": {"template": {"_type": "proxy_embedding", "proxy_url": {"l2": false, "info": "", "list": false, "name": "proxy_url", "show": false, "type": "str", "value": "", "l2_name": "proxy_url", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "http://*************:8080"}}, "description": " 使用自建的embedding服务使用openai进行embed ", "base_classes": ["Embeddings"], "display_name": "OpenAIProxyEmbedding", "output_types": [], "custom_fields": {}, "documentation": ""}, "type": "OpenAIProxyEmbedding", "value": null}, "selected": true, "positionAbsolute": {"x": 585.3522336200281, "y": 1118.8504631949986}}, {"width": 384, "height": 254, "id": "InputFileNode-jdLkB", "type": "genericNode", "position": {"x": -67.64298275986698, "y": 405.6605420525051}, "data": {"id": "InputFileNode-jdLkB", "node": {"template": {"_type": "InputFileNode", "file_path": {"l2": false, "info": "", "list": false, "name": "file_path", "show": true, "type": "file", "value": "", "l2_name": "file_path", "advanced": false, "password": false, "required": false, "suffixes": [".html", ".md", ".txt", ".jpg", ".png", ".jpeg", ".csv", ".doc", ".docx", ".pdf", ".ppt", ".pptx", ".xlsx", ".tiff"], "fileTypes": ["html", "md", "txt", "jpg", "png", "jpeg", "csv", "doc", "docx", "pdf", "ppt", "pptx", "tiff", "xlsx"], "file_path": null, "multiline": false, "placeholder": "", "display_name": "输入内容"}}, "description": "输入节点，用来自动对接输入", "base_classes": ["fileNode"], "display_name": "InputFileNode", "output_types": [], "custom_fields": {}, "documentation": ""}, "type": "InputFileNode", "value": null}, "selected": true, "positionAbsolute": {"x": -67.64298275986698, "y": 405.6605420525051}}, {"width": 384, "height": 500, "id": "ProxyChatLLM-rnE4s", "type": "genericNode", "position": {"x": 598.1704660724836, "y": -139.8508856646713}, "data": {"id": "ProxyChatLLM-rnE4s", "node": {"template": {"n": {"l2": false, "info": "", "list": false, "name": "n", "show": false, "type": "int", "value": 1, "l2_name": "n", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "tags": {"l2": false, "info": "", "list": true, "name": "tags", "show": false, "type": "str", "l2_name": "tags", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "_type": "ProxyChatLLM", "cache": {"l2": false, "info": "", "list": false, "name": "cache", "show": false, "type": "bool", "l2_name": "cache", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "top_p": {"l2": false, "info": "", "list": false, "name": "top_p", "show": true, "type": "float", "value": 0.9, "l2_name": "top_p", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "client": {"l2": false, "info": "", "list": false, "name": "client", "show": false, "type": "Any", "l2_name": "client", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "headers": {"l2": false, "info": "", "list": false, "name": "headers", "show": false, "type": "code", "value": "", "l2_name": "headers", "advanced": false, "password": false, "required": false, "multiline": true, "placeholder": ""}, "verbose": {"l2": false, "info": "", "list": false, "name": "verbose", "show": false, "type": "bool", "value": false, "l2_name": "verbose", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "metadata": {"l2": false, "info": "", "list": false, "name": "metadata", "show": false, "type": "code", "l2_name": "metadata", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "callbacks": {"l2": false, "info": "", "list": true, "name": "callbacks", "show": false, "type": "langchain.callbacks.base.BaseCallbackHandler", "l2_name": "callbacks", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "streaming": {"l2": false, "info": "", "list": false, "name": "streaming", "show": false, "type": "bool", "value": false, "l2_name": "streaming", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "max_tokens": {"l2": false, "info": "", "list": false, "name": "max_tokens", "show": true, "type": "int", "value": "", "l2_name": "max_tokens", "advanced": true, "password": true, "required": false, "multiline": false, "placeholder": ""}, "model_name": {"l2": false, "info": "", "list": false, "name": "model_name", "show": true, "type": "str", "value": "gpt-3.5-turbo-16k-0613", "l2_name": "model_name", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "max_retries": {"l2": false, "info": "", "list": false, "name": "max_retries", "show": false, "type": "int", "value": 6, "l2_name": "max_retries", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "temperature": {"l2": false, "info": "", "list": false, "name": "temperature", "show": true, "type": "float", "value": 0.7, "l2_name": "temperature", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "model_kwargs": {"l2": false, "info": "", "list": false, "name": "model_kwargs", "show": true, "type": "code", "l2_name": "model_kwargs", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "elemai_api_key": {"l2": false, "info": "", "list": false, "name": "elemai_api_key", "show": true, "type": "str", "value": "", "l2_name": "elemai_api_key", "advanced": false, "password": true, "required": false, "multiline": false, "placeholder": ""}, "elemai_base_url": {"l2": false, "info": "", "list": false, "name": "elemai_base_url", "show": true, "type": "str", "value": "http://192.168.106.12:6001/v1/chatcompletion", "l2_name": "elemai_base_url", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "request_timeout": {"l2": false, "info": "", "list": false, "name": "request_timeout", "show": false, "type": "float", "l2_name": "request_timeout", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "tiktoken_model_name": {"l2": false, "info": "", "list": false, "name": "tiktoken_model_name", "show": false, "type": "str", "l2_name": "tiktoken_model_name", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}}, "description": "Wrapper around proxy Chat large language models.", "base_classes": ["BaseLanguageModel", "ProxyChatLLM", "BaseChatModel", "BaseLLM"], "display_name": "ProxyChatLLM", "output_types": [], "custom_fields": {}, "documentation": ""}, "type": "ProxyChatLLM", "value": null}, "selected": true, "positionAbsolute": {"x": 598.1704660724836, "y": -139.8508856646713}}, {"width": 384, "height": 346, "id": "Milvus-T3kRH", "type": "genericNode", "position": {"x": 1356.1418476973313, "y": 809.6096821830637}, "data": {"id": "Milvus-T3kRH", "node": {"template": {"_type": "<PERSON><PERSON><PERSON><PERSON>", "drop_old": {"l2": false, "info": "", "list": false, "name": "drop_old", "show": false, "type": "bool", "value": false, "l2_name": "drop_old", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "documents": {"l2": false, "info": "", "list": true, "name": "documents", "show": true, "type": "Document", "l2_name": "documents", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Documents"}, "embedding": {"l2": false, "info": "", "list": false, "name": "embedding", "show": true, "type": "Embeddings", "l2_name": "embedding", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "display_name": "Embedding"}, "metadatas": {"l2": false, "info": "", "list": true, "name": "metadatas", "show": false, "type": "code", "l2_name": "metadatas", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "index_params": {"l2": false, "info": "", "list": false, "name": "index_params", "show": false, "type": "code", "l2_name": "index_params", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "search_kwargs": {"l2": false, "info": "", "list": false, "name": "search_kwargs", "show": true, "type": "code", "value": "{}", "l2_name": "search_kwargs", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "search_params": {"l2": false, "info": "", "list": false, "name": "search_params", "show": false, "type": "code", "l2_name": "search_params", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "collection_name": {"l2": false, "info": "", "list": false, "name": "collection_name", "show": true, "type": "str", "value": "", "l2_name": "collection_name", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "collection_id": ""}, "connection_args": {"l2": false, "info": "", "list": false, "name": "connection_args", "show": true, "type": "code", "value": "", "l2_name": "connection_args", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "consistency_level": {"l2": false, "info": "", "list": false, "name": "consistency_level", "show": false, "type": "str", "value": "Session", "l2_name": "consistency_level", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}}, "description": "Create a Milvus collection, indexes it with HNSW, and insert data.", "base_classes": ["VectorStore", "<PERSON><PERSON><PERSON><PERSON>", "BaseRetriever", "VectorStoreRetriever"], "display_name": "<PERSON><PERSON><PERSON><PERSON>", "output_types": [], "custom_fields": {}, "documentation": "http://***************:8030"}, "type": "<PERSON><PERSON><PERSON><PERSON>", "value": null}, "selected": true, "positionAbsolute": {"x": 1356.1418476973313, "y": 809.6096821830637}}, {"width": 384, "height": 392, "id": "ElemUnstructuredLoaderV0-LAGXM", "type": "genericNode", "position": {"x": 370.3915867302851, "y": 393.863205643299}, "data": {"id": "ElemUnstructuredLoaderV0-LAGXM", "node": {"template": {"_type": "ElemUnstructuredLoaderV0", "metadata": {"info": "", "list": false, "name": "metadata", "show": true, "type": "code", "value": "{}", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "display_name": "<PERSON><PERSON><PERSON>"}, "file_path": {"info": "", "list": false, "name": "file_path", "show": true, "type": "fileNode", "value": "", "advanced": false, "password": false, "required": false, "suffixes": [".html", ".md", ".txt", ".jpg", ".png", ".jpeg", ".csv", ".doc", ".docx", ".pdf", ".ppt", ".pptx", ".xlsx", ".tiff"], "fileTypes": ["html", "md", "txt", "jpg", "png", "jpeg", "csv", "doc", "docx", "pdf", "ppt", "pptx", "tiff", "xlsx"], "multiline": false, "placeholder": ""}, "unstructured_api_url": {"info": "", "list": false, "name": "unstructured_api_url", "show": true, "type": "str", "value": "", "advanced": false, "password": true, "required": true, "multiline": false, "placeholder": "", "display_name": "unstructured_api_url"}}, "description": "Loads a PDF with pypdf and chunks at character level. dummy version", "base_classes": ["Document"], "display_name": "ElemUnstructuredLoaderV0", "output_types": ["Document"], "custom_fields": {}, "documentation": ""}, "type": "ElemUnstructuredLoaderV0", "value": null}, "selected": true, "positionAbsolute": {"x": 370.3915867302851, "y": 393.863205643299}}, {"width": 384, "height": 540, "id": "RecursiveCharacterTextSplitter-a7fc9", "type": "genericNode", "position": {"x": 840.0634079971517, "y": 439.4498034537745}, "data": {"id": "RecursiveCharacterTextSplitter-a7fc9", "node": {"template": {"_type": "RecursiveCharacterTextSplitter", "documents": {"info": "", "list": false, "name": "documents", "show": true, "type": "Document", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}, "chunk_size": {"info": "", "list": false, "name": "chunk_size", "show": true, "type": "int", "value": 1000, "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "display_name": "Chunk Size"}, "separators": {"info": "", "list": false, "name": "separators", "show": true, "type": "str", "value": "\\n\\n", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "display_name": "Separator"}, "chunk_overlap": {"info": "", "list": false, "name": "chunk_overlap", "show": true, "type": "int", "value": 200, "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "display_name": "<PERSON><PERSON>"}, "separator_type": {"info": "", "list": true, "name": "separator_type", "show": true, "type": "str", "value": "Text", "options": ["Text", "cobol", "cpp", "csharp", "go", "html", "java", "js", "kotlin", "latex", "markdown", "php", "proto", "python", "rst", "ruby", "rust", "scala", "sol", "swift", "ts"], "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "display_name": "Separator Type"}}, "description": "Splitting text by recursively look at characters.", "base_classes": ["Document"], "display_name": "RecursiveCharacterTextSplitter", "output_types": ["Document"], "custom_fields": {}, "documentation": "https://python.langchain.com/docs/modules/data_connection/document_transformers/text_splitters/recursive_text_splitter"}, "type": "RecursiveCharacterTextSplitter", "value": null}, "selected": true, "positionAbsolute": {"x": 840.0634079971517, "y": 439.4498034537745}}], "edges": [{"source": "CombineDocsChain-Pud2p", "target": "RetrievalQA-qH6Mk", "sourceHandle": "CombineDocsChain|CombineDocsChain-Pud2p|BaseCombineDocumentsChain|function", "targetHandle": "BaseCombineDocumentsChain|combine_documents_chain|RetrievalQA-qH6Mk", "id": "reactflow__edge-CombineDocsChain-Pud2pCombineDocsChain|CombineDocsChain-Pud2p|BaseCombineDocumentsChain|function-RetrievalQA-qH6MkBaseCombineDocumentsChain|combine_documents_chain|RetrievalQA-qH6Mk", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "ProxyChatLLM-rnE4s", "target": "CombineDocsChain-Pud2p", "sourceHandle": "ProxyChatLLM|ProxyChatLLM-rnE4s|BaseLanguageModel|ProxyChatLLM|BaseChatModel|BaseLLM", "targetHandle": "BaseLanguageModel|llm|CombineDocsChain-Pud2p", "id": "reactflow__edge-ProxyChatLLM-rnE4sProxyChatLLM|ProxyChatLLM-rnE4s|BaseLanguageModel|ProxyChatLLM|BaseChatModel|BaseLLM-CombineDocsChain-Pud2pBaseLanguageModel|llm|CombineDocsChain-Pud2p", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "Milvus-T3kRH", "target": "RetrievalQA-qH6Mk", "sourceHandle": "Milvus|Milvus-T3kRH|VectorStore|Milvus|BaseRetriever|VectorStoreRetriever", "targetHandle": "BaseRetriever|retriever|RetrievalQA-qH6Mk", "id": "reactflow__edge-Milvus-T3kRHMilvus|Milvus-T3kRH|VectorStore|Milvus|BaseRetriever|VectorStoreRetriever-RetrievalQA-qH6MkBaseRetriever|retriever|RetrievalQA-qH6Mk", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "OpenAIProxyEmbedding-yvld7", "target": "Milvus-T3kRH", "sourceHandle": "OpenAIProxyEmbedding|OpenAIProxyEmbedding-yvld7|Embeddings", "targetHandle": "Embeddings|embedding|Milvus-T3kRH", "id": "reactflow__edge-OpenAIProxyEmbedding-yvld7OpenAIProxyEmbedding|OpenAIProxyEmbedding-yvld7|Embeddings-Milvus-T3kRHEmbeddings|embedding|Milvus-T3kRH", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "InputFileNode-jdLkB", "target": "ElemUnstructuredLoaderV0-LAGXM", "sourceHandle": "InputFileNode|InputFileNode-jdLkB|fileNode", "targetHandle": "fileNode|file_path|ElemUnstructuredLoaderV0-LAGXM", "id": "reactflow__edge-InputFileNode-jdLkBInputFileNode|InputFileNode-jdLkB|fileNode-ElemUnstructuredLoaderV0-LAGXMfileNode|file_path|ElemUnstructuredLoaderV0-LAGXM", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "ElemUnstructuredLoaderV0-LAGXM", "target": "RecursiveCharacterTextSplitter-a7fc9", "sourceHandle": "ElemUnstructuredLoaderV0|ElemUnstructuredLoaderV0-LAGXM|Document", "targetHandle": "Document|documents|RecursiveCharacterTextSplitter-a7fc9", "id": "reactflow__edge-ElemUnstructuredLoaderV0-LAGXMElemUnstructuredLoaderV0|ElemUnstructuredLoaderV0-LAGXM|Document-RecursiveCharacterTextSplitter-a7fc9Document|documents|RecursiveCharacterTextSplitter-a7fc9", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "RecursiveCharacterTextSplitter-a7fc9", "target": "Milvus-T3kRH", "sourceHandle": "RecursiveCharacterTextSplitter|RecursiveCharacterTextSplitter-a7fc9|Document", "targetHandle": "Document|documents|Milvus-T3kRH", "id": "reactflow__edge-RecursiveCharacterTextSplitter-a7fc9RecursiveCharacterTextSplitter|RecursiveCharacterTextSplitter-a7fc9|Document-Milvus-T3kRHDocument|documents|Milvus-T3kRH", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}], "viewport": {"zoom": 1, "x": 0, "y": 0}}, "is_component": false, "name": "", "description": "", "id": "2bec4", "status": 0, "write": false, "guide_word": ""}}}}, {"知识库问答_严谨版": {"id": "RetrievalQA-011e4", "type": "RetrievalQA", "node": {"output_types": [], "display_name": "知识库问答_严谨版", "documentation": "", "base_classes": ["RetrievalQA", "BaseRetrievalQA", "Chain", "function"], "description": "Chain for question-answering against an index.", "template": {"n_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "n", "show": false, "type": "int", "value": 1, "l2_name": "n", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "n"}, "display_name": "N"}, "tags_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": true, "name": "tags", "show": false, "type": "str", "l2_name": "tags", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "tags"}, "display_name": "Tags"}, "cache_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "cache", "show": false, "type": "bool", "l2_name": "cache", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "cache"}, "display_name": "<PERSON><PERSON>"}, "top_p_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "top_p", "show": true, "type": "float", "value": 0.9, "l2_name": "top_p", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "top_p"}, "display_name": "Top P"}, "client_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "client", "show": false, "type": "Any", "l2_name": "client", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "client"}, "display_name": "Client"}, "headers_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "headers", "show": false, "type": "code", "value": "", "l2_name": "headers", "advanced": true, "password": false, "required": false, "multiline": true, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "headers"}, "display_name": "Headers"}, "verbose_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "verbose", "show": false, "type": "bool", "value": false, "l2_name": "verbose", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "verbose"}, "display_name": "Verbose"}, "metadata_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "metadata", "show": false, "type": "code", "l2_name": "metadata", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "metadata"}, "display_name": "<PERSON><PERSON><PERSON>"}, "callbacks_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": true, "name": "callbacks", "show": false, "type": "langchain.callbacks.base.BaseCallbackHandler", "l2_name": "callbacks", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "callbacks"}, "display_name": "Callbacks"}, "streaming_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "streaming", "show": false, "type": "bool", "value": false, "l2_name": "streaming", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "streaming"}, "display_name": "Streaming"}, "max_tokens_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "max_tokens", "show": true, "type": "int", "value": "", "l2_name": "max_tokens", "advanced": true, "password": true, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "max_tokens"}, "display_name": "<PERSON>"}, "model_name_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "model_name", "show": true, "type": "str", "value": "gpt-3.5-turbo-16k-0613", "l2_name": "model_name", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "model_name"}, "display_name": "Model Name"}, "max_retries_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "max_retries", "show": false, "type": "int", "value": 6, "l2_name": "max_retries", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "max_retries"}, "display_name": "Max Retries"}, "temperature_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "temperature", "show": true, "type": "float", "value": 0.7, "l2_name": "temperature", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "temperature"}, "display_name": "Temperature"}, "model_kwargs_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "model_kwargs", "show": false, "type": "code", "l2_name": "model_kwargs", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "model_kwargs"}, "display_name": "Model Kwargs"}, "elemai_api_key_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "elemai_api_key", "show": true, "type": "str", "value": "", "l2_name": "elemai_api_key", "advanced": true, "password": true, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "elemai_api_key"}, "display_name": "Elemai Api Key"}, "elemai_base_url_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "elemai_base_url", "show": true, "type": "str", "value": "http://192.168.106.12:6001/v1/chatcompletion", "l2_name": "elemai_base_url", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "elemai_base_url"}, "display_name": "Elemai Base Url"}, "request_timeout_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "request_timeout", "show": false, "type": "float", "l2_name": "request_timeout", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "request_timeout"}, "display_name": "Request Timeout"}, "tiktoken_model_name_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "tiktoken_model_name", "show": false, "type": "str", "l2_name": "tiktoken_model_name", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "tiktoken_model_name"}, "display_name": "Tiktoken Model Name"}, "tags_RetrievalQA-ypS4m": {"l2": false, "info": "", "list": true, "name": "tags", "show": false, "type": "str", "l2_name": "tags", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-ypS4m", "field": "tags"}, "display_name": "Tags"}, "memory_RetrievalQA-ypS4m": {"l2": false, "info": "", "list": false, "name": "memory", "show": true, "type": "BaseMemory", "l2_name": "memory", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-ypS4m", "field": "memory"}, "display_name": "Memory"}, "verbose_RetrievalQA-ypS4m": {"l2": false, "info": "", "list": false, "name": "verbose", "show": false, "type": "bool", "l2_name": "verbose", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-ypS4m", "field": "verbose"}, "display_name": "Verbose"}, "metadata_RetrievalQA-ypS4m": {"l2": false, "info": "", "list": false, "name": "metadata", "show": false, "type": "code", "l2_name": "metadata", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-ypS4m", "field": "metadata"}, "display_name": "<PERSON><PERSON><PERSON>"}, "callbacks_RetrievalQA-ypS4m": {"l2": false, "info": "", "list": true, "name": "callbacks", "show": false, "type": "langchain.callbacks.base.BaseCallbackHandler", "l2_name": "callbacks", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-ypS4m", "field": "callbacks"}, "display_name": "Callbacks"}, "input_key_RetrievalQA-ypS4m": {"l2": false, "info": "", "list": false, "name": "input_key", "show": true, "type": "str", "value": "query", "l2_name": "input_key", "advanced": true, "password": false, "required": true, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-ypS4m", "field": "input_key"}, "display_name": "Input Key"}, "input_node_RetrievalQA-ypS4m": {"l2": false, "info": "", "list": false, "name": "input_node", "show": true, "type": "input", "l2_name": "input_node", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Preset Question", "proxy": {"id": "RetrievalQA-ypS4m", "field": "input_node"}}, "output_key_RetrievalQA-ypS4m": {"l2": false, "info": "", "list": false, "name": "output_key", "show": true, "type": "str", "value": "result", "l2_name": "output_key", "advanced": true, "password": false, "required": true, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-ypS4m", "field": "output_key"}, "display_name": "Output Key"}, "return_source_documents_RetrievalQA-ypS4m": {"l2": false, "info": "", "list": false, "name": "return_source_documents", "show": true, "type": "bool", "value": true, "l2_name": "return_source_documents", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-ypS4m", "field": "return_source_documents"}, "display_name": "Return Source Documents"}, "token_max_CombineDocsChain-bMaXk": {"l2": false, "info": "只对Stuff类型生效", "list": false, "name": "token_max", "show": true, "type": "int", "value": 7000, "l2_name": "token_max", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "token_max", "proxy": {"id": "CombineDocsChain-bMaXk", "field": "token_max"}}, "chain_type_CombineDocsChain-bMaXk": {"l2": false, "info": "", "list": true, "name": "chain_type", "show": true, "type": "str", "value": "stuff", "l2_name": "chain_type", "options": ["stuff", "map_reduce", "map_rerank", "refine"], "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "proxy": {"id": "CombineDocsChain-bMaXk", "field": "chain_type"}, "display_name": "Chain Type"}, "document_prompt_CombineDocsChain-bMaXk": {"info": "", "list": false, "name": "document_prompt", "type": "BasePromptTemplate", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "CombineDocsChain-bMaXk", "field": "document_prompt"}, "display_name": "Document Prompt"}, "context_PromptTemplate-gM5zg": {"info": "", "list": false, "name": "context", "show": true, "type": "str", "value": "", "advanced": false, "password": false, "required": false, "multiline": true, "input_types": ["Document", "BaseOutputParser", "VariableNode"], "placeholder": "", "display_name": "context", "proxy": {"id": "PromptTemplate-gM5zg", "field": "context"}}, "question_PromptTemplate-gM5zg": {"info": "", "list": false, "name": "question", "show": true, "type": "str", "value": "", "advanced": false, "password": false, "required": false, "multiline": true, "input_types": ["Document", "BaseOutputParser", "VariableNode"], "placeholder": "", "display_name": "question", "proxy": {"id": "PromptTemplate-gM5zg", "field": "question"}}, "template_PromptTemplate-gM5zg": {"l2": false, "info": "", "list": false, "name": "template", "show": true, "type": "prompt", "value": "参考文本：\n{context}\n\n----------------------\n# Role：客服\n\n# Background :\n- 你是一名客服人员。你的日常工作是根据【参考文本】的内容回复用户的各类咨询问题。\n\n# Goals:\n- 以上【参考文本】中是从企业知识库中查找到的相关信息，只能结合以上信息进行回答，若以上内容为空或其中没有找到能回答【用户问题】的内容时，则回复“没有找到相关内容”，不能根据你自己的知识自己发挥。\n- 特别要注意区分【用户问题】与【参考文本】中不同的日期、人名、公司名这些关键信息。\n- 用中文回答问题，并且答案要严谨、专业、清晰、可读性好。\n- 拥有排版审美, 会利用序号, 缩进, 分隔线和换行符等等来美化信息排版。\n\n----------------------\n\n用户问题: {question}\n你的回答:", "l2_name": "template", "advanced": false, "password": false, "required": true, "multiline": true, "placeholder": "", "proxy": {"id": "PromptTemplate-gM5zg", "field": "template"}, "display_name": "Template"}, "output_parser_PromptTemplate-gM5zg": {"l2": false, "info": "", "list": false, "name": "output_parser", "show": true, "type": "BaseOutputParser", "l2_name": "output_parser", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-gM5zg", "field": "output_parser"}, "display_name": "Output Parser"}, "input_variables_PromptTemplate-gM5zg": {"l2": false, "info": "", "list": true, "name": "input_variables", "show": false, "type": "str", "value": ["context", "question"], "l2_name": "input_variables", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-gM5zg", "field": "input_variables"}, "display_name": "Input Variables"}, "template_format_PromptTemplate-gM5zg": {"l2": false, "info": "", "list": false, "name": "template_format", "show": false, "type": "str", "value": "f-string", "l2_name": "template_format", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-gM5zg", "field": "template_format"}, "display_name": "Template Format"}, "partial_variables_PromptTemplate-gM5zg": {"l2": false, "info": "", "list": false, "name": "partial_variables", "show": false, "type": "code", "l2_name": "partial_variables", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-gM5zg", "field": "partial_variables"}, "display_name": "Partial Variables"}, "validate_template_PromptTemplate-gM5zg": {"l2": false, "info": "", "list": false, "name": "validate_template", "show": false, "type": "bool", "value": true, "l2_name": "validate_template", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-gM5zg", "field": "validate_template"}, "display_name": "Validate Template"}, "ids_ElasticKeywordsSearch-dVhq9": {"l2": false, "info": "", "list": true, "name": "ids", "show": false, "type": "str", "l2_name": "ids", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ElasticKeywordsSearch-dVhq9", "field": "ids"}, "display_name": "Ids"}, "documents_ElasticKeywordsSearch-dVhq9": {"l2": false, "info": "", "list": true, "name": "documents", "show": true, "type": "Document", "l2_name": "documents", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Documents", "proxy": {"id": "ElasticKeywordsSearch-dVhq9", "field": "documents"}}, "embedding_ElasticKeywordsSearch-dVhq9": {"l2": false, "info": "", "list": false, "name": "embedding", "show": false, "type": "Embeddings", "l2_name": "embedding", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Embedding", "proxy": {"id": "ElasticKeywordsSearch-dVhq9", "field": "embedding"}}, "metadatas_ElasticKeywordsSearch-dVhq9": {"l2": false, "info": "", "list": true, "name": "metadatas", "show": false, "type": "code", "l2_name": "metadatas", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ElasticKeywordsSearch-dVhq9", "field": "metadatas"}, "display_name": "Metadatas"}, "index_name_ElasticKeywordsSearch-dVhq9": {"l2": true, "info": "", "list": false, "name": "index_name", "show": true, "type": "str", "value": "col_1701960496_3a537dc9", "l2_name": "知识库", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ElasticKeywordsSearch-dVhq9", "field": "index_name"}, "display_name": "Index Name"}, "ssl_verify_ElasticKeywordsSearch-dVhq9": {"l2": false, "info": "", "list": false, "name": "ssl_verify", "show": true, "type": "str", "value": "", "l2_name": "ssl_verify", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "ssl_verify", "proxy": {"id": "ElasticKeywordsSearch-dVhq9", "field": "ssl_verify"}}, "search_kwargs_ElasticKeywordsSearch-dVhq9": {"l2": false, "info": "", "list": false, "name": "search_kwargs", "show": false, "type": "code", "value": "{\"k\":6,\"query_strategy\":\"match_phrase\",\"must_or_should\":\"must\"}", "l2_name": "search_kwargs", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ElasticKeywordsSearch-dVhq9", "field": "search_kwargs"}, "display_name": "Search Kwargs"}, "refresh_indices_ElasticKeywordsSearch-dVhq9": {"l2": false, "info": "", "list": false, "name": "refresh_indices", "show": false, "type": "bool", "value": true, "l2_name": "refresh_indices", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ElasticKeywordsSearch-dVhq9", "field": "refresh_indices"}, "display_name": "Refresh Indices"}, "elasticsearch_url_ElasticKeywordsSearch-dVhq9": {"l2": false, "info": "", "list": false, "name": "elasticsearch_url", "show": true, "type": "str", "value": "", "l2_name": "elasticsearch_url", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "ES_connection_url", "proxy": {"id": "ElasticKeywordsSearch-dVhq9", "field": "elasticsearch_url"}}, "question_PromptTemplate-4iBFj": {"info": "", "list": false, "name": "question", "show": true, "type": "str", "value": "", "advanced": false, "password": false, "required": false, "multiline": true, "input_types": ["Document", "BaseOutputParser", "VariableNode"], "placeholder": "", "display_name": "question", "proxy": {"id": "PromptTemplate-4iBFj", "field": "question"}}, "template_PromptTemplate-4iBFj": {"l2": false, "info": "", "list": false, "name": "template", "show": true, "type": "prompt", "value": "分析给定Question，提取Question中包含的KeyWords，输出列表形式\n\nExamples:\nQuestion: 数据项素2022年营收是多少？\nKeyWords: ['数据项素', '2022', '营收, '营业收入']\nQuestion: 深圳出差住宿标准是什么？\nKeyWords: ['深圳', '出差', '住宿标准', '报销标准']\n\n\n----------------\nQuestion: {question}\nKeyWords: ", "l2_name": "template", "advanced": false, "password": false, "required": true, "multiline": true, "placeholder": "", "proxy": {"id": "PromptTemplate-4iBFj", "field": "template"}, "display_name": "Template"}, "output_parser_PromptTemplate-4iBFj": {"l2": false, "info": "", "list": false, "name": "output_parser", "show": true, "type": "BaseOutputParser", "l2_name": "output_parser", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-4iBFj", "field": "output_parser"}, "display_name": "Output Parser"}, "input_variables_PromptTemplate-4iBFj": {"l2": false, "info": "", "list": true, "name": "input_variables", "show": false, "type": "str", "value": ["question"], "l2_name": "input_variables", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-4iBFj", "field": "input_variables"}, "display_name": "Input Variables"}, "template_format_PromptTemplate-4iBFj": {"l2": false, "info": "", "list": false, "name": "template_format", "show": false, "type": "str", "value": "f-string", "l2_name": "template_format", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-4iBFj", "field": "template_format"}, "display_name": "Template Format"}, "partial_variables_PromptTemplate-4iBFj": {"l2": false, "info": "", "list": false, "name": "partial_variables", "show": false, "type": "code", "l2_name": "partial_variables", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-4iBFj", "field": "partial_variables"}, "display_name": "Partial Variables"}, "validate_template_PromptTemplate-4iBFj": {"l2": false, "info": "", "list": false, "name": "validate_template", "show": false, "type": "bool", "value": true, "l2_name": "validate_template", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-4iBFj", "field": "validate_template"}, "display_name": "Validate Template"}}, "flow": {"data": {"nodes": [{"width": 384, "height": 500, "id": "ProxyChatLLM-UYoB4", "type": "genericNode", "position": {"x": -2220.2561641720736, "y": -777.5185403158035}, "data": {"id": "ProxyChatLLM-UYoB4", "node": {"template": {"n": {"l2": false, "info": "", "list": false, "name": "n", "show": false, "type": "int", "value": 1, "l2_name": "n", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "tags": {"l2": false, "info": "", "list": true, "name": "tags", "show": false, "type": "str", "l2_name": "tags", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "_type": "ProxyChatLLM", "cache": {"l2": false, "info": "", "list": false, "name": "cache", "show": false, "type": "bool", "l2_name": "cache", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "top_p": {"l2": false, "info": "", "list": false, "name": "top_p", "show": true, "type": "float", "value": 0.9, "l2_name": "top_p", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "client": {"l2": false, "info": "", "list": false, "name": "client", "show": false, "type": "Any", "l2_name": "client", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "headers": {"l2": false, "info": "", "list": false, "name": "headers", "show": false, "type": "code", "value": "", "l2_name": "headers", "advanced": false, "password": false, "required": false, "multiline": true, "placeholder": ""}, "verbose": {"l2": false, "info": "", "list": false, "name": "verbose", "show": false, "type": "bool", "value": false, "l2_name": "verbose", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "metadata": {"l2": false, "info": "", "list": false, "name": "metadata", "show": false, "type": "code", "l2_name": "metadata", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "callbacks": {"l2": false, "info": "", "list": true, "name": "callbacks", "show": false, "type": "langchain.callbacks.base.BaseCallbackHandler", "l2_name": "callbacks", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "streaming": {"l2": false, "info": "", "list": false, "name": "streaming", "show": false, "type": "bool", "value": false, "l2_name": "streaming", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "max_tokens": {"l2": false, "info": "", "list": false, "name": "max_tokens", "show": true, "type": "int", "value": "", "l2_name": "max_tokens", "advanced": true, "password": true, "required": false, "multiline": false, "placeholder": ""}, "model_name": {"l2": false, "info": "", "list": false, "name": "model_name", "show": true, "type": "str", "value": "gpt-3.5-turbo-16k-0613", "l2_name": "model_name", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "max_retries": {"l2": false, "info": "", "list": false, "name": "max_retries", "show": false, "type": "int", "value": 6, "l2_name": "max_retries", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "temperature": {"l2": false, "info": "", "list": false, "name": "temperature", "show": true, "type": "float", "value": 0.7, "l2_name": "temperature", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "model_kwargs": {"l2": false, "info": "", "list": false, "name": "model_kwargs", "show": true, "type": "code", "l2_name": "model_kwargs", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "elemai_api_key": {"l2": false, "info": "", "list": false, "name": "elemai_api_key", "show": true, "type": "str", "value": "", "l2_name": "elemai_api_key", "advanced": false, "password": true, "required": false, "multiline": false, "placeholder": ""}, "elemai_base_url": {"l2": false, "info": "", "list": false, "name": "elemai_base_url", "show": true, "type": "str", "value": "http://192.168.106.12:6001/v1/chatcompletion", "l2_name": "elemai_base_url", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "request_timeout": {"l2": false, "info": "", "list": false, "name": "request_timeout", "show": false, "type": "float", "l2_name": "request_timeout", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "tiktoken_model_name": {"l2": false, "info": "", "list": false, "name": "tiktoken_model_name", "show": false, "type": "str", "l2_name": "tiktoken_model_name", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}}, "description": "Wrapper around proxy Chat large language models.", "base_classes": ["BaseChatModel", "ProxyChatLLM", "BaseLanguageModel", "BaseLLM"], "display_name": "ProxyChatLLM", "output_types": [], "custom_fields": {}, "documentation": ""}, "type": "ProxyChatLLM", "value": null}, "selected": true, "positionAbsolute": {"x": -2220.2561641720736, "y": -777.5185403158035}}, {"width": 384, "height": 316, "id": "RetrievalQA-ypS4m", "type": "genericNode", "position": {"x": -523.0150819181391, "y": 97.4334598716227}, "data": {"id": "RetrievalQA-ypS4m", "node": {"template": {"tags": {"l2": false, "info": "", "list": true, "name": "tags", "show": false, "type": "str", "l2_name": "tags", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "_type": "RetrievalQA", "memory": {"l2": false, "info": "", "list": false, "name": "memory", "show": true, "type": "BaseMemory", "l2_name": "memory", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "verbose": {"l2": false, "info": "", "list": false, "name": "verbose", "show": false, "type": "bool", "l2_name": "verbose", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "metadata": {"l2": false, "info": "", "list": false, "name": "metadata", "show": false, "type": "code", "l2_name": "metadata", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "callbacks": {"l2": false, "info": "", "list": true, "name": "callbacks", "show": false, "type": "langchain.callbacks.base.BaseCallbackHandler", "l2_name": "callbacks", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "input_key": {"l2": false, "info": "", "list": false, "name": "input_key", "show": true, "type": "str", "value": "query", "l2_name": "input_key", "advanced": true, "password": false, "required": true, "multiline": false, "placeholder": ""}, "retriever": {"l2": false, "info": "", "list": false, "name": "retriever", "show": true, "type": "BaseRetriever", "l2_name": "retriever", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}, "input_node": {"l2": false, "info": "", "list": false, "name": "input_node", "show": true, "type": "input", "l2_name": "input_node", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Preset Question"}, "output_key": {"l2": false, "info": "", "list": false, "name": "output_key", "show": true, "type": "str", "value": "result", "l2_name": "output_key", "advanced": true, "password": false, "required": true, "multiline": false, "placeholder": ""}, "combine_documents_chain": {"l2": false, "info": "", "list": false, "name": "combine_documents_chain", "show": true, "type": "BaseCombineDocumentsChain", "l2_name": "combine_documents_chain", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}, "return_source_documents": {"l2": false, "info": "", "list": false, "name": "return_source_documents", "show": true, "type": "bool", "value": true, "l2_name": "return_source_documents", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}}, "description": "Chain for question-answering against an index.", "base_classes": ["RetrievalQA", "BaseRetrievalQA", "Chain", "function"], "display_name": "RetrievalQA", "output_types": [], "custom_fields": {}, "documentation": "https://python.langchain.com/docs/modules/chains/popular/vector_db_qa"}, "type": "RetrievalQA", "value": null}, "selected": true, "positionAbsolute": {"x": -523.0150819181391, "y": 97.4334598716227}}, {"width": 384, "height": 412, "id": "CombineDocsChain-bMaXk", "type": "genericNode", "position": {"x": -1130.4006392228448, "y": -400.50282859447026}, "data": {"id": "CombineDocsChain-bMaXk", "node": {"template": {"llm": {"l2": false, "info": "", "list": false, "name": "llm", "show": true, "type": "BaseLanguageModel", "l2_name": "llm", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "display_name": "LLM"}, "_type": "load_qa_chain", "prompt": {"l2": false, "info": "只对Stuff类型生效", "list": false, "name": "prompt", "show": true, "type": "BasePromptTemplate", "l2_name": "prompt", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "prompt"}, "token_max": {"l2": false, "info": "只对Stuff类型生效", "list": false, "name": "token_max", "show": true, "type": "int", "value": 7000, "l2_name": "token_max", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "token_max"}, "chain_type": {"l2": false, "info": "", "list": true, "name": "chain_type", "show": true, "type": "str", "value": "stuff", "l2_name": "chain_type", "options": ["stuff", "map_reduce", "map_rerank", "refine"], "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}, "document_prompt": {"info": "", "list": false, "name": "document_prompt", "type": "BasePromptTemplate", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}}, "description": "Load question answering chain.", "base_classes": ["BaseCombineDocumentsChain", "function"], "display_name": "CombineDocsChain", "output_types": [], "custom_fields": {}, "documentation": ""}, "type": "CombineDocsChain", "value": null}, "selected": true, "positionAbsolute": {"x": -1130.4006392228448, "y": -400.50282859447026}}, {"width": 384, "height": 454, "id": "PromptTemplate-gM5zg", "type": "genericNode", "position": {"x": -1632.2039543868736, "y": -814.266749101787}, "data": {"id": "PromptTemplate-gM5zg", "node": {"name": "", "template": {"_type": "PromptTemplate", "context": {"info": "", "list": false, "name": "context", "show": true, "type": "str", "value": "", "advanced": false, "password": false, "required": false, "multiline": true, "input_types": ["Document", "BaseOutputParser", "VariableNode"], "placeholder": "", "display_name": "context"}, "question": {"info": "", "list": false, "name": "question", "show": true, "type": "str", "value": "", "advanced": false, "password": false, "required": false, "multiline": true, "input_types": ["Document", "BaseOutputParser", "VariableNode"], "placeholder": "", "display_name": "question"}, "template": {"l2": false, "info": "", "list": false, "name": "template", "show": true, "type": "prompt", "value": "参考文本：\n{context}\n\n----------------------\n# Role：客服\n\n# Background :\n- 你是一名客服人员。你的日常工作是根据【参考文本】的内容回复用户的各类咨询问题。\n\n# Goals:\n- 以上【参考文本】中是从企业知识库中查找到的相关信息，只能结合以上信息进行回答，若以上内容为空或其中没有找到能回答【用户问题】的内容时，则回复“没有找到相关内容”，不能根据你自己的知识自己发挥。\n- 特别要注意区分【用户问题】与【参考文本】中不同的日期、人名、公司名这些关键信息。\n- 用中文回答问题，并且答案要严谨、专业、清晰、可读性好。\n- 拥有排版审美, 会利用序号, 缩进, 分隔线和换行符等等来美化信息排版。\n\n----------------------\n\n用户问题: {question}\n你的回答:", "l2_name": "template", "advanced": false, "password": false, "required": true, "multiline": true, "placeholder": ""}, "output_parser": {"l2": false, "info": "", "list": false, "name": "output_parser", "show": true, "type": "BaseOutputParser", "l2_name": "output_parser", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "input_variables": {"l2": false, "info": "", "list": true, "name": "input_variables", "show": false, "type": "str", "value": ["context", "question"], "l2_name": "input_variables", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}, "template_format": {"l2": false, "info": "", "list": false, "name": "template_format", "show": false, "type": "str", "value": "f-string", "l2_name": "template_format", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "partial_variables": {"l2": false, "info": "", "list": false, "name": "partial_variables", "show": false, "type": "code", "l2_name": "partial_variables", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "validate_template": {"l2": false, "info": "", "list": false, "name": "validate_template", "show": false, "type": "bool", "value": true, "l2_name": "validate_template", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}}, "description": "<PERSON><PERSON><PERSON> to represent a prompt for an LLM.", "base_classes": ["StringPromptTemplate", "BasePromptTemplate", "PromptTemplate"], "display_name": "PromptTemplate", "output_types": [], "custom_fields": {"": ["context", "question"], "template": ["context", "question"]}, "documentation": "https://python.langchain.com/docs/modules/model_io/prompts/prompt_templates/", "field_formatters": {"formatters": {"openai_api_key": {}}, "base_formatters": {"dict": {}, "list": {}, "show": {}, "union": {}, "kwargs": {}, "default": {}, "headers": {}, "optional": {}, "password": {}, "multiline": {}, "model_fields": {"MODEL_DICT": {"OpenAI": ["text-davinci-003", "text-davinci-002", "text-curie-001", "text-babbage-001", "text-ada-001"], "Anthropic": ["claude-v1", "claude-v1-100k", "claude-instant-v1", "claude-instant-v1-100k", "claude-v1.3", "claude-v1.3-100k", "claude-v1.2", "claude-v1.0", "claude-instant-v1.1", "claude-instant-v1.1-100k", "claude-instant-v1.0"], "ChatOpenAI": ["gpt-3.5-turbo-0613", "gpt-3.5-turbo", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-16k", "gpt-4-0613", "gpt-4-32k-0613", "gpt-4", "gpt-4-32k"], "ChatAnthropic": ["claude-v1", "claude-v1-100k", "claude-instant-v1", "claude-instant-v1-100k", "claude-v1.3", "claude-v1.3-100k", "claude-v1.2", "claude-v1.0", "claude-instant-v1.1", "claude-instant-v1.1-100k", "claude-instant-v1.0"]}}, "dict_code_file": {}}}}, "type": "PromptTemplate", "value": null}, "selected": true, "positionAbsolute": {"x": -1632.2039543868736, "y": -814.266749101787}}, {"width": 384, "height": 628, "id": "ElasticKeywordsSearch-dVhq9", "type": "genericNode", "position": {"x": -1620.6487761012545, "y": -186.12742019985436}, "data": {"id": "ElasticKeywordsSearch-dVhq9", "node": {"template": {"ids": {"l2": false, "info": "", "list": true, "name": "ids", "show": false, "type": "str", "l2_name": "ids", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "llm": {"l2": false, "info": "", "list": false, "name": "llm", "show": true, "type": "BaseLLM", "l2_name": "llm", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "LLM"}, "_type": "ElasticKeywordsSearch", "prompt": {"l2": false, "info": "", "list": false, "name": "prompt", "show": true, "type": "BasePromptTemplate", "l2_name": "prompt", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "prompt"}, "documents": {"l2": false, "info": "", "list": true, "name": "documents", "show": true, "type": "Document", "l2_name": "documents", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Documents"}, "embedding": {"l2": false, "info": "", "list": false, "name": "embedding", "show": false, "type": "Embeddings", "l2_name": "embedding", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Embedding"}, "metadatas": {"l2": false, "info": "", "list": true, "name": "metadatas", "show": false, "type": "code", "l2_name": "metadatas", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "index_name": {"l2": true, "info": "", "list": false, "name": "index_name", "show": true, "type": "str", "value": "col_1701960496_3a537dc9", "l2_name": "知识库", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "ssl_verify": {"l2": false, "info": "", "list": false, "name": "ssl_verify", "show": true, "type": "str", "value": "", "l2_name": "ssl_verify", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "ssl_verify"}, "search_kwargs": {"l2": false, "info": "", "list": false, "name": "search_kwargs", "show": true, "type": "code", "value": "{\"k\":6,\"query_strategy\":\"match_phrase\",\"must_or_should\":\"must\"}", "l2_name": "search_kwargs", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "refresh_indices": {"l2": false, "info": "", "list": false, "name": "refresh_indices", "show": false, "type": "bool", "value": true, "l2_name": "refresh_indices", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "elasticsearch_url": {"l2": false, "info": "", "list": false, "name": "elasticsearch_url", "show": true, "type": "str", "value": "", "l2_name": "elasticsearch_url", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "ES_connection_url"}}, "description": "Construct ElasticKeywordsSearch wrapper from raw documents.", "base_classes": ["ElasticKeywordsSearch", "VectorStore", "BaseRetriever", "VectorStoreRetriever"], "display_name": "ElasticKeywordsSearch", "output_types": [], "custom_fields": {}, "documentation": "http://***************:8030"}, "type": "ElasticKeywordsSearch", "value": null}, "selected": true, "positionAbsolute": {"x": -1620.6487761012545, "y": -186.12742019985436}}, {"width": 384, "height": 372, "id": "PromptTemplate-4iBFj", "type": "genericNode", "position": {"x": -2302.5673221463094, "y": -146.52073590180217}, "data": {"id": "PromptTemplate-4iBFj", "node": {"name": "", "template": {"_type": "PromptTemplate", "question": {"info": "", "list": false, "name": "question", "show": true, "type": "str", "value": "", "advanced": false, "password": false, "required": false, "multiline": true, "input_types": ["Document", "BaseOutputParser", "VariableNode"], "placeholder": "", "display_name": "question"}, "template": {"l2": false, "info": "", "list": false, "name": "template", "show": true, "type": "prompt", "value": "分析给定Question，提取Question中包含的KeyWords，输出列表形式\n\nExamples:\nQuestion: 数据项素2022年营收是多少？\nKeyWords: ['数据项素', '2022', '营收, '营业收入']\nQuestion: 深圳出差住宿标准是什么？\nKeyWords: ['深圳', '出差', '住宿标准', '报销标准']\n\n\n----------------\nQuestion: {question}\nKeyWords: ", "l2_name": "template", "advanced": false, "password": false, "required": true, "multiline": true, "placeholder": ""}, "output_parser": {"l2": false, "info": "", "list": false, "name": "output_parser", "show": true, "type": "BaseOutputParser", "l2_name": "output_parser", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "input_variables": {"l2": false, "info": "", "list": true, "name": "input_variables", "show": false, "type": "str", "value": ["question"], "l2_name": "input_variables", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}, "template_format": {"l2": false, "info": "", "list": false, "name": "template_format", "show": false, "type": "str", "value": "f-string", "l2_name": "template_format", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "partial_variables": {"l2": false, "info": "", "list": false, "name": "partial_variables", "show": false, "type": "code", "l2_name": "partial_variables", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "validate_template": {"l2": false, "info": "", "list": false, "name": "validate_template", "show": false, "type": "bool", "value": true, "l2_name": "validate_template", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}}, "description": "<PERSON><PERSON><PERSON> to represent a prompt for an LLM.", "base_classes": ["StringPromptTemplate", "BasePromptTemplate", "PromptTemplate"], "display_name": "PromptTemplate", "output_types": [], "custom_fields": {"": ["question"], "template": ["question"]}, "documentation": "https://python.langchain.com/docs/modules/model_io/prompts/prompt_templates/", "field_formatters": {"formatters": {"openai_api_key": {}}, "base_formatters": {"dict": {}, "list": {}, "show": {}, "union": {}, "kwargs": {}, "default": {}, "headers": {}, "optional": {}, "password": {}, "multiline": {}, "model_fields": {"MODEL_DICT": {"OpenAI": ["text-davinci-003", "text-davinci-002", "text-curie-001", "text-babbage-001", "text-ada-001"], "Anthropic": ["claude-v1", "claude-v1-100k", "claude-instant-v1", "claude-instant-v1-100k", "claude-v1.3", "claude-v1.3-100k", "claude-v1.2", "claude-v1.0", "claude-instant-v1.1", "claude-instant-v1.1-100k", "claude-instant-v1.0"], "ChatOpenAI": ["gpt-3.5-turbo-0613", "gpt-3.5-turbo", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-16k", "gpt-4-0613", "gpt-4-32k-0613", "gpt-4", "gpt-4-32k"], "ChatAnthropic": ["claude-v1", "claude-v1-100k", "claude-instant-v1", "claude-instant-v1-100k", "claude-v1.3", "claude-v1.3-100k", "claude-v1.2", "claude-v1.0", "claude-instant-v1.1", "claude-instant-v1.1-100k", "claude-instant-v1.0"]}}, "dict_code_file": {}}}}, "type": "PromptTemplate", "value": null}, "selected": true, "positionAbsolute": {"x": -2302.5673221463094, "y": -146.52073590180217}}], "edges": [{"source": "CombineDocsChain-bMaXk", "target": "RetrievalQA-ypS4m", "sourceHandle": "CombineDocsChain|CombineDocsChain-bMaXk|BaseCombineDocumentsChain|function", "targetHandle": "BaseCombineDocumentsChain|combine_documents_chain|RetrievalQA-ypS4m", "id": "reactflow__edge-CombineDocsChain-bMaXkCombineDocsChain|CombineDocsChain-bMaXk|BaseCombineDocumentsChain|function-RetrievalQA-ypS4mBaseCombineDocumentsChain|combine_documents_chain|RetrievalQA-ypS4m", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "PromptTemplate-gM5zg", "target": "CombineDocsChain-bMaXk", "sourceHandle": "PromptTemplate|PromptTemplate-gM5zg|StringPromptTemplate|BasePromptTemplate|PromptTemplate", "targetHandle": "BasePromptTemplate|prompt|CombineDocsChain-bMaXk", "id": "reactflow__edge-PromptTemplate-gM5zgPromptTemplate|PromptTemplate-gM5zg|StringPromptTemplate|BasePromptTemplate|PromptTemplate-CombineDocsChain-bMaXkBasePromptTemplate|prompt|CombineDocsChain-bMaXk", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "ElasticKeywordsSearch-dVhq9", "target": "RetrievalQA-ypS4m", "sourceHandle": "ElasticKeywordsSearch|ElasticKeywordsSearch-dVhq9|ElasticKeywordsSearch|VectorStore|BaseRetriever|VectorStoreRetriever", "targetHandle": "BaseRetriever|retriever|RetrievalQA-ypS4m", "id": "reactflow__edge-ElasticKeywordsSearch-dVhq9ElasticKeywordsSearch|ElasticKeywordsSearch-dVhq9|ElasticKeywordsSearch|VectorStore|BaseRetriever|VectorStoreRetriever-RetrievalQA-ypS4mBaseRetriever|retriever|RetrievalQA-ypS4m", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "PromptTemplate-4iBFj", "target": "ElasticKeywordsSearch-dVhq9", "sourceHandle": "PromptTemplate|PromptTemplate-4iBFj|StringPromptTemplate|BasePromptTemplate|PromptTemplate", "targetHandle": "BasePromptTemplate|prompt|ElasticKeywordsSearch-dVhq9", "id": "reactflow__edge-PromptTemplate-4iBFjPromptTemplate|PromptTemplate-4iBFj|StringPromptTemplate|BasePromptTemplate|PromptTemplate-ElasticKeywordsSearch-dVhq9BasePromptTemplate|prompt|ElasticKeywordsSearch-dVhq9", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "ProxyChatLLM-UYoB4", "target": "CombineDocsChain-bMaXk", "sourceHandle": "ProxyChatLLM|ProxyChatLLM-UYoB4|BaseChatModel|ProxyChatLLM|BaseLanguageModel|BaseLLM", "targetHandle": "BaseLanguageModel|llm|CombineDocsChain-bMaXk", "id": "reactflow__edge-ProxyChatLLM-UYoB4ProxyChatLLM|ProxyChatLLM-UYoB4|BaseChatModel|ProxyChatLLM|BaseLanguageModel|BaseLLM-CombineDocsChain-bMaXkBaseLanguageModel|llm|CombineDocsChain-bMaXk", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "ProxyChatLLM-UYoB4", "target": "ElasticKeywordsSearch-dVhq9", "sourceHandle": "ProxyChatLLM|ProxyChatLLM-UYoB4|BaseChatModel|ProxyChatLLM|BaseLanguageModel|BaseLLM", "targetHandle": "BaseLLM|llm|ElasticKeywordsSearch-dVhq9", "id": "reactflow__edge-ProxyChatLLM-UYoB4ProxyChatLLM|ProxyChatLLM-UYoB4|BaseChatModel|ProxyChatLLM|BaseLanguageModel|BaseLLM-ElasticKeywordsSearch-dVhq9BaseLLM|llm|ElasticKeywordsSearch-dVhq9", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}], "viewport": {"zoom": 1, "x": 0, "y": 0}}, "is_component": false, "name": "", "description": "", "id": "e4a9c", "status": 0, "write": false, "guide_word": ""}}}}, {"知识库问答_增强版": {"id": "ConversationalRetrievalChain-f19da", "type": "ConversationalRetrievalChain", "node": {"output_types": [], "display_name": "知识库问答_增强版", "documentation": "", "base_classes": ["BaseConversationalRetrievalChain", "Chain", "ConversationalRetrievalChain", "function"], "description": "Convenience method to load chain from LLM and retriever.", "template": {"n_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "n", "show": false, "type": "int", "value": 1, "l2_name": "n", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "n"}, "display_name": "N"}, "tags_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": true, "name": "tags", "show": false, "type": "str", "l2_name": "tags", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "tags"}, "display_name": "Tags"}, "cache_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "cache", "show": false, "type": "bool", "l2_name": "cache", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "cache"}, "display_name": "<PERSON><PERSON>"}, "top_p_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "top_p", "show": true, "type": "float", "value": 0.9, "l2_name": "top_p", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "top_p"}, "display_name": "Top P"}, "client_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "client", "show": false, "type": "Any", "l2_name": "client", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "client"}, "display_name": "Client"}, "headers_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "headers", "show": false, "type": "code", "value": "", "l2_name": "headers", "advanced": true, "password": false, "required": false, "multiline": true, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "headers"}, "display_name": "Headers"}, "verbose_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "verbose", "show": false, "type": "bool", "value": false, "l2_name": "verbose", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "verbose"}, "display_name": "Verbose"}, "metadata_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "metadata", "show": false, "type": "code", "l2_name": "metadata", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "metadata"}, "display_name": "<PERSON><PERSON><PERSON>"}, "callbacks_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": true, "name": "callbacks", "show": false, "type": "langchain.callbacks.base.BaseCallbackHandler", "l2_name": "callbacks", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "callbacks"}, "display_name": "Callbacks"}, "streaming_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "streaming", "show": false, "type": "bool", "value": false, "l2_name": "streaming", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "streaming"}, "display_name": "Streaming"}, "max_tokens_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "max_tokens", "show": true, "type": "int", "value": "", "l2_name": "max_tokens", "advanced": true, "password": true, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "max_tokens"}, "display_name": "<PERSON>"}, "model_name_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "model_name", "show": true, "type": "str", "value": "gpt-3.5-turbo-16k-0613", "l2_name": "model_name", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "model_name"}, "display_name": "Model Name"}, "max_retries_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "max_retries", "show": false, "type": "int", "value": 6, "l2_name": "max_retries", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "max_retries"}, "display_name": "Max Retries"}, "temperature_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "temperature", "show": true, "type": "float", "value": 0.7, "l2_name": "temperature", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "temperature"}, "display_name": "Temperature"}, "model_kwargs_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "model_kwargs", "show": false, "type": "code", "l2_name": "model_kwargs", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "model_kwargs"}, "display_name": "Model Kwargs"}, "elemai_api_key_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "elemai_api_key", "show": true, "type": "str", "value": "", "l2_name": "elemai_api_key", "advanced": true, "password": true, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "elemai_api_key"}, "display_name": "Elemai Api Key"}, "elemai_base_url_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "elemai_base_url", "show": true, "type": "str", "value": "http://192.168.106.12:6001/v1/chatcompletion", "l2_name": "elemai_base_url", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "elemai_base_url"}, "display_name": "Elemai Base Url"}, "request_timeout_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "request_timeout", "show": false, "type": "float", "l2_name": "request_timeout", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "request_timeout"}, "display_name": "Request Timeout"}, "tiktoken_model_name_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "tiktoken_model_name", "show": false, "type": "str", "l2_name": "tiktoken_model_name", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "tiktoken_model_name"}, "display_name": "Tiktoken Model Name"}, "tags_MixEsVectorRetriever-J35CZ": {"l2": false, "info": "", "list": true, "name": "tags", "show": true, "type": "str", "l2_name": "tags", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "MixEsVectorRetriever-J35CZ", "field": "tags"}, "display_name": "Tags"}, "metadata_MixEsVectorRetriever-J35CZ": {"l2": false, "info": "", "list": false, "name": "metadata", "show": false, "type": "code", "l2_name": "metadata", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "MixEsVectorRetriever-J35CZ", "field": "metadata"}, "display_name": "<PERSON><PERSON><PERSON>"}, "combine_strategy_MixEsVectorRetriever-J35CZ": {"l2": false, "info": "", "list": true, "name": "combine_strategy", "show": true, "type": "str", "value": "mix", "l2_name": "combine_strategy", "options": ["keyword_front", "vector_front", "mix"], "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "proxy": {"id": "MixEsVectorRetriever-J35CZ", "field": "combine_strategy"}, "display_name": "Combine Strategy"}, "drop_old_Milvus-cyR5W": {"l2": false, "info": "", "list": false, "name": "drop_old", "show": false, "type": "bool", "value": false, "l2_name": "drop_old", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-cyR5W", "field": "drop_old"}, "display_name": "Drop Old"}, "documents_Milvus-cyR5W": {"l2": false, "info": "", "list": true, "name": "documents", "show": true, "type": "Document", "l2_name": "documents", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Documents", "proxy": {"id": "Milvus-cyR5W", "field": "documents"}}, "embedding_Milvus-cyR5W": {"l2": false, "info": "", "list": false, "name": "embedding", "show": false, "type": "Embeddings", "l2_name": "embedding", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Embedding", "proxy": {"id": "Milvus-cyR5W", "field": "embedding"}}, "metadatas_Milvus-cyR5W": {"l2": false, "info": "", "list": true, "name": "metadatas", "show": false, "type": "code", "l2_name": "metadatas", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-cyR5W", "field": "metadatas"}, "display_name": "Metadatas"}, "index_params_Milvus-cyR5W": {"l2": false, "info": "", "list": false, "name": "index_params", "show": false, "type": "code", "l2_name": "index_params", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-cyR5W", "field": "index_params"}, "display_name": "Index Params"}, "search_kwargs_Milvus-cyR5W": {"l2": false, "info": "", "list": false, "name": "search_kwargs", "show": false, "type": "code", "value": "{}", "l2_name": "search_kwargs", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-cyR5W", "field": "search_kwargs"}, "display_name": "Search Kwargs"}, "search_params_Milvus-cyR5W": {"l2": false, "info": "", "list": false, "name": "search_params", "show": false, "type": "code", "l2_name": "search_params", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-cyR5W", "field": "search_params"}, "display_name": "Search Params"}, "collection_name_Milvus-cyR5W": {"l2": true, "info": "", "list": false, "name": "collection_name", "show": true, "type": "str", "value": "col_1700798314_e3f656ab", "l2_name": "collection_name", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-cyR5W", "field": "collection_name"}, "display_name": "Collection Name"}, "connection_args_Milvus-cyR5W": {"l2": false, "info": "", "list": false, "name": "connection_args", "show": false, "type": "code", "value": "", "l2_name": "connection_args", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-cyR5W", "field": "connection_args"}, "display_name": "Connection Args"}, "consistency_level_Milvus-cyR5W": {"l2": false, "info": "", "list": false, "name": "consistency_level", "show": false, "type": "str", "value": "Session", "l2_name": "consistency_level", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-cyR5W", "field": "consistency_level"}, "display_name": "Consistency Level"}, "ids_ElasticKeywordsSearch-31Et9": {"l2": false, "info": "", "list": true, "name": "ids", "show": false, "type": "str", "l2_name": "ids", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ElasticKeywordsSearch-31Et9", "field": "ids"}, "display_name": "Ids"}, "llm_ElasticKeywordsSearch-31Et9": {"l2": false, "info": "", "list": false, "name": "llm", "type": "BaseLLM", "l2_name": "llm", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "LLM", "proxy": {"id": "ElasticKeywordsSearch-31Et9", "field": "llm"}}, "prompt_ElasticKeywordsSearch-31Et9": {"l2": false, "info": "", "list": false, "name": "prompt", "type": "BasePromptTemplate", "l2_name": "prompt", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "prompt", "proxy": {"id": "ElasticKeywordsSearch-31Et9", "field": "prompt"}}, "documents_ElasticKeywordsSearch-31Et9": {"l2": false, "info": "", "list": true, "name": "documents", "show": true, "type": "Document", "l2_name": "documents", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Documents", "proxy": {"id": "ElasticKeywordsSearch-31Et9", "field": "documents"}}, "embedding_ElasticKeywordsSearch-31Et9": {"l2": false, "info": "", "list": false, "name": "embedding", "show": false, "type": "Embeddings", "l2_name": "embedding", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Embedding", "proxy": {"id": "ElasticKeywordsSearch-31Et9", "field": "embedding"}}, "metadatas_ElasticKeywordsSearch-31Et9": {"l2": false, "info": "", "list": true, "name": "metadatas", "show": false, "type": "code", "l2_name": "metadatas", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ElasticKeywordsSearch-31Et9", "field": "metadatas"}, "display_name": "Metadatas"}, "index_name_ElasticKeywordsSearch-31Et9": {"l2": true, "info": "", "list": false, "name": "index_name", "show": true, "type": "str", "value": "col_1700798314_e3f656ab", "l2_name": "index_name", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ElasticKeywordsSearch-31Et9", "field": "index_name"}, "display_name": "Index Name"}, "ssl_verify_ElasticKeywordsSearch-31Et9": {"l2": false, "info": "", "list": false, "name": "ssl_verify", "show": true, "type": "str", "value": "", "l2_name": "ssl_verify", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "ssl_verify", "proxy": {"id": "ElasticKeywordsSearch-31Et9", "field": "ssl_verify"}}, "search_kwargs_ElasticKeywordsSearch-31Et9": {"l2": false, "info": "", "list": false, "name": "search_kwargs", "show": false, "type": "code", "value": "{}", "l2_name": "search_kwargs", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ElasticKeywordsSearch-31Et9", "field": "search_kwargs"}, "display_name": "Search Kwargs"}, "refresh_indices_ElasticKeywordsSearch-31Et9": {"l2": false, "info": "", "list": false, "name": "refresh_indices", "show": false, "type": "bool", "value": true, "l2_name": "refresh_indices", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ElasticKeywordsSearch-31Et9", "field": "refresh_indices"}, "display_name": "Refresh Indices"}, "elasticsearch_url_ElasticKeywordsSearch-31Et9": {"l2": false, "info": "", "list": false, "name": "elasticsearch_url", "show": true, "type": "str", "value": "", "l2_name": "elasticsearch_url", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "ES_connection_url", "proxy": {"id": "ElasticKeywordsSearch-31Et9", "field": "elasticsearch_url"}}, "verbose_ConversationalRetrievalChain-SmY3w": {"l2": false, "info": "", "list": false, "name": "verbose", "show": false, "type": "bool", "value": false, "l2_name": "verbose", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ConversationalRetrievalChain-SmY3w", "field": "verbose"}, "display_name": "Verbose"}, "callbacks_ConversationalRetrievalChain-SmY3w": {"l2": false, "info": "", "list": false, "name": "callbacks", "show": false, "type": "Callbacks", "l2_name": "callbacks", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ConversationalRetrievalChain-SmY3w", "field": "callbacks"}, "display_name": "Callbacks"}, "chain_type_ConversationalRetrievalChain-SmY3w": {"l2": false, "info": "", "list": true, "name": "chain_type", "show": true, "type": "str", "value": "stuff", "l2_name": "chain_type", "options": ["stuff", "map_reduce", "map_rerank", "refine"], "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "proxy": {"id": "ConversationalRetrievalChain-SmY3w", "field": "chain_type"}, "display_name": "Chain Type"}, "input_node_ConversationalRetrievalChain-SmY3w": {"l2": false, "info": "", "list": false, "name": "input_node", "show": true, "type": "input", "l2_name": "input_node", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Preset Question", "proxy": {"id": "ConversationalRetrievalChain-SmY3w", "field": "input_node"}}, "condense_question_llm_ConversationalRetrievalChain-SmY3w": {"l2": false, "info": "", "list": false, "name": "condense_question_llm", "show": false, "type": "BaseLanguageModel", "l2_name": "condense_question_llm", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ConversationalRetrievalChain-SmY3w", "field": "condense_question_llm"}, "display_name": "Condense Question LLM"}, "return_source_documents_ConversationalRetrievalChain-SmY3w": {"l2": false, "info": "", "list": false, "name": "return_source_documents", "show": true, "type": "bool", "value": true, "l2_name": "return_source_documents", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Return source documents", "proxy": {"id": "ConversationalRetrievalChain-SmY3w", "field": "return_source_documents"}}, "condense_question_prompt_ConversationalRetrievalChain-SmY3w": {"l2": false, "info": "", "list": false, "name": "condense_question_prompt", "show": true, "type": "BasePromptTemplate", "value": {"_type": "prompt", "template": "Given the following conversation and a follow up question, rephrase the follow up question to be a standalone question, in its original language.\n\nChat History:\n{chat_history}\nFollow Up Input: {question}\nStandalone question:", "output_parser": null, "input_variables": ["chat_history", "question"], "template_format": "f-string", "partial_variables": {}, "validate_template": true}, "l2_name": "condense_question_prompt", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ConversationalRetrievalChain-SmY3w", "field": "condense_question_prompt"}, "display_name": "Condense Question Prompt"}, "ai_prefix_ConversationBufferMemory-K1tss": {"l2": false, "info": "", "list": false, "name": "ai_prefix", "show": false, "type": "str", "value": "AI", "l2_name": "ai_prefix", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ConversationBufferMemory-K1tss", "field": "ai_prefix"}, "display_name": "Ai Prefix"}, "input_key_ConversationBufferMemory-K1tss": {"l2": false, "info": "The variable to be used as Chat Input when more than one variable is available.", "list": false, "name": "input_key", "show": true, "type": "str", "value": "", "l2_name": "input_key", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ConversationBufferMemory-K1tss", "field": "input_key"}, "display_name": "Input Key"}, "memory_key_ConversationBufferMemory-K1tss": {"l2": false, "info": "", "list": false, "name": "memory_key", "show": true, "type": "str", "value": "chat_history", "l2_name": "memory_key", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ConversationBufferMemory-K1tss", "field": "memory_key"}, "display_name": "Memory Key"}, "output_key_ConversationBufferMemory-K1tss": {"l2": false, "info": "The variable to be used as Chat Output (e.g. answer in a ConversationalRetrievalChain)", "list": false, "name": "output_key", "show": true, "type": "str", "value": "answer", "l2_name": "output_key", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ConversationBufferMemory-K1tss", "field": "output_key"}, "display_name": "Output Key"}, "chat_memory_ConversationBufferMemory-K1tss": {"l2": false, "info": "", "list": false, "name": "chat_memory", "show": true, "type": "BaseChatMessageHistory", "l2_name": "chat_memory", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ConversationBufferMemory-K1tss", "field": "chat_memory"}, "display_name": "Chat Memory"}, "human_prefix_ConversationBufferMemory-K1tss": {"l2": false, "info": "", "list": false, "name": "human_prefix", "show": false, "type": "str", "value": "Human", "l2_name": "human_prefix", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ConversationBufferMemory-K1tss", "field": "human_prefix"}, "display_name": "Human Prefix"}, "return_messages_ConversationBufferMemory-K1tss": {"l2": false, "info": "", "list": false, "name": "return_messages", "show": true, "type": "bool", "l2_name": "return_messages", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ConversationBufferMemory-K1tss", "field": "return_messages"}, "display_name": "Return Messages"}, "template_PromptTemplate-bs0vj": {"l2": false, "info": "", "list": false, "name": "template", "show": true, "type": "prompt", "value": "参考文本：\n{context}\n\n----------------------\n\n1.用中文回答用户问题，并且答案要严谨专业。\n2.你需要依据以上【参考文本】中的内容来回答，当【参考文本】中有明确与用户问题相关的内容时才进行回答，不可根据自己的知识来回答。\n3.由于【参考文本】可能包含多个来自不同信息源的信息，所以根据这些不同的信息源可能得出有差异甚至冲突的答案，当发现这种情况时，这些答案都列举出来；如果没有冲突或差异，则只需要给出一个最终结果。\n4.若【参考文本】中内容与用户问题不相关则回复“没有找到相关内容”。\n\n----------------------\n\n用户问题: {question}\n你的回答:", "l2_name": "template", "advanced": false, "password": false, "required": true, "multiline": true, "placeholder": "", "proxy": {"id": "PromptTemplate-bs0vj", "field": "template"}, "display_name": "Template"}, "output_parser_PromptTemplate-bs0vj": {"l2": false, "info": "", "list": false, "name": "output_parser", "show": true, "type": "BaseOutputParser", "l2_name": "output_parser", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-bs0vj", "field": "output_parser"}, "display_name": "Output Parser"}, "input_variables_PromptTemplate-bs0vj": {"l2": false, "info": "", "list": true, "name": "input_variables", "show": false, "type": "str", "value": ["context", "question"], "l2_name": "input_variables", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-bs0vj", "field": "input_variables"}, "display_name": "Input Variables"}, "template_format_PromptTemplate-bs0vj": {"l2": false, "info": "", "list": false, "name": "template_format", "show": false, "type": "str", "value": "f-string", "l2_name": "template_format", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-bs0vj", "field": "template_format"}, "display_name": "Template Format"}, "partial_variables_PromptTemplate-bs0vj": {"l2": false, "info": "", "list": false, "name": "partial_variables", "show": false, "type": "code", "l2_name": "partial_variables", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-bs0vj", "field": "partial_variables"}, "display_name": "Partial Variables"}, "validate_template_PromptTemplate-bs0vj": {"l2": false, "info": "", "list": false, "name": "validate_template", "show": false, "type": "bool", "value": true, "l2_name": "validate_template", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-bs0vj", "field": "validate_template"}, "display_name": "Validate Template"}}, "flow": {"data": {"nodes": [{"width": 384, "height": 500, "id": "ProxyChatLLM-UYoB4", "type": "genericNode", "position": {"x": -3019.932873785603, "y": -759.0579388411045}, "data": {"id": "ProxyChatLLM-UYoB4", "node": {"template": {"n": {"l2": false, "info": "", "list": false, "name": "n", "show": false, "type": "int", "value": 1, "l2_name": "n", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "tags": {"l2": false, "info": "", "list": true, "name": "tags", "show": false, "type": "str", "l2_name": "tags", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "_type": "ProxyChatLLM", "cache": {"l2": false, "info": "", "list": false, "name": "cache", "show": false, "type": "bool", "l2_name": "cache", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "top_p": {"l2": false, "info": "", "list": false, "name": "top_p", "show": true, "type": "float", "value": 0.9, "l2_name": "top_p", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "client": {"l2": false, "info": "", "list": false, "name": "client", "show": false, "type": "Any", "l2_name": "client", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "headers": {"l2": false, "info": "", "list": false, "name": "headers", "show": false, "type": "code", "value": "", "l2_name": "headers", "advanced": false, "password": false, "required": false, "multiline": true, "placeholder": ""}, "verbose": {"l2": false, "info": "", "list": false, "name": "verbose", "show": false, "type": "bool", "value": false, "l2_name": "verbose", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "metadata": {"l2": false, "info": "", "list": false, "name": "metadata", "show": false, "type": "code", "l2_name": "metadata", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "callbacks": {"l2": false, "info": "", "list": true, "name": "callbacks", "show": false, "type": "langchain.callbacks.base.BaseCallbackHandler", "l2_name": "callbacks", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "streaming": {"l2": false, "info": "", "list": false, "name": "streaming", "show": false, "type": "bool", "value": false, "l2_name": "streaming", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "max_tokens": {"l2": false, "info": "", "list": false, "name": "max_tokens", "show": true, "type": "int", "value": "", "l2_name": "max_tokens", "advanced": true, "password": true, "required": false, "multiline": false, "placeholder": ""}, "model_name": {"l2": false, "info": "", "list": false, "name": "model_name", "show": true, "type": "str", "value": "gpt-3.5-turbo-16k-0613", "l2_name": "model_name", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "max_retries": {"l2": false, "info": "", "list": false, "name": "max_retries", "show": false, "type": "int", "value": 6, "l2_name": "max_retries", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "temperature": {"l2": false, "info": "", "list": false, "name": "temperature", "show": true, "type": "float", "value": 0.7, "l2_name": "temperature", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "model_kwargs": {"l2": false, "info": "", "list": false, "name": "model_kwargs", "show": true, "type": "code", "l2_name": "model_kwargs", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "elemai_api_key": {"l2": false, "info": "", "list": false, "name": "elemai_api_key", "show": true, "type": "str", "value": "", "l2_name": "elemai_api_key", "advanced": false, "password": true, "required": false, "multiline": false, "placeholder": ""}, "elemai_base_url": {"l2": false, "info": "", "list": false, "name": "elemai_base_url", "show": true, "type": "str", "value": "http://192.168.106.12:6001/v1/chatcompletion", "l2_name": "elemai_base_url", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "request_timeout": {"l2": false, "info": "", "list": false, "name": "request_timeout", "show": false, "type": "float", "l2_name": "request_timeout", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "tiktoken_model_name": {"l2": false, "info": "", "list": false, "name": "tiktoken_model_name", "show": false, "type": "str", "l2_name": "tiktoken_model_name", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}}, "description": "Wrapper around proxy Chat large language models.", "base_classes": ["ProxyChatLLM", "BaseLanguageModel", "BaseChatModel", "BaseLLM"], "display_name": "ProxyChatLLM", "output_types": [], "custom_fields": {}, "documentation": ""}, "type": "ProxyChatLLM", "value": null}, "selected": true, "positionAbsolute": {"x": -3019.932873785603, "y": -759.0579388411045}}, {"width": 384, "height": 350, "id": "MixEsVectorRetriever-J35CZ", "type": "genericNode", "position": {"x": -2407.3760484096842, "y": -129.80633940439208}, "data": {"id": "MixEsVectorRetriever-J35CZ", "node": {"template": {"tags": {"l2": false, "info": "", "list": true, "name": "tags", "show": true, "type": "str", "l2_name": "tags", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "_type": "MixEsVectorRetriever", "metadata": {"l2": false, "info": "", "list": false, "name": "metadata", "show": true, "type": "code", "l2_name": "metadata", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "combine_strategy": {"l2": false, "info": "", "list": true, "name": "combine_strategy", "show": true, "type": "str", "value": "mix", "l2_name": "combine_strategy", "options": ["keyword_front", "vector_front", "mix"], "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}, "vector_retriever": {"l2": false, "info": "", "list": false, "name": "vector_retriever", "show": true, "type": "BaseRetriever", "l2_name": "vector_retriever", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}, "keyword_retriever": {"l2": false, "info": "", "list": false, "name": "keyword_retriever", "show": true, "type": "BaseRetriever", "l2_name": "keyword_retriever", "advanced": false, "password": true, "required": true, "multiline": false, "placeholder": "", "value": ""}}, "description": "This class ensemble the results of es retriever and vector retriever.", "base_classes": ["BaseRetriever", "MixEsVectorRetriever"], "display_name": "MixEsVectorRetriever", "output_types": [], "custom_fields": {}, "documentation": ""}, "type": "MixEsVectorRetriever", "value": null}, "selected": true, "positionAbsolute": {"x": -2407.3760484096842, "y": -129.80633940439208}}, {"width": 384, "height": 392, "id": "Milvus-cyR5W", "type": "genericNode", "position": {"x": -3010.2766638860544, "y": 280.83609057857933}, "data": {"id": "Milvus-cyR5W", "node": {"template": {"_type": "<PERSON><PERSON><PERSON><PERSON>", "drop_old": {"l2": false, "info": "", "list": false, "name": "drop_old", "show": false, "type": "bool", "value": false, "l2_name": "drop_old", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "documents": {"l2": false, "info": "", "list": true, "name": "documents", "show": true, "type": "Document", "l2_name": "documents", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Documents"}, "embedding": {"l2": false, "info": "", "list": false, "name": "embedding", "show": false, "type": "Embeddings", "l2_name": "embedding", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Embedding"}, "metadatas": {"l2": false, "info": "", "list": true, "name": "metadatas", "show": false, "type": "code", "l2_name": "metadatas", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "index_params": {"l2": false, "info": "", "list": false, "name": "index_params", "show": false, "type": "code", "l2_name": "index_params", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "search_kwargs": {"l2": false, "info": "", "list": false, "name": "search_kwargs", "show": true, "type": "code", "value": "{}", "l2_name": "search_kwargs", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "search_params": {"l2": false, "info": "", "list": false, "name": "search_params", "show": false, "type": "code", "l2_name": "search_params", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "collection_name": {"l2": true, "info": "", "list": false, "name": "collection_name", "show": true, "type": "str", "value": "col_1700798314_e3f656ab", "l2_name": "collection_name", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "connection_args": {"l2": false, "info": "", "list": false, "name": "connection_args", "show": true, "type": "code", "value": "", "l2_name": "connection_args", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "consistency_level": {"l2": false, "info": "", "list": false, "name": "consistency_level", "show": false, "type": "str", "value": "Session", "l2_name": "consistency_level", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}}, "description": "Create a Milvus collection, indexes it with HNSW, and insert data.", "base_classes": ["VectorStore", "<PERSON><PERSON><PERSON><PERSON>", "BaseRetriever", "VectorStoreRetriever"], "display_name": "<PERSON><PERSON><PERSON><PERSON>", "output_types": [], "custom_fields": {}, "documentation": "http://***************:8030"}, "type": "<PERSON><PERSON><PERSON><PERSON>", "value": null}, "selected": true, "positionAbsolute": {"x": -3010.2766638860544, "y": 280.83609057857933}}, {"width": 384, "height": 474, "id": "ElasticKeywordsSearch-31Et9", "type": "genericNode", "position": {"x": -3007.9415392561414, "y": -220.71638201134618}, "data": {"id": "ElasticKeywordsSearch-31Et9", "node": {"template": {"ids": {"l2": false, "info": "", "list": true, "name": "ids", "show": false, "type": "str", "l2_name": "ids", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "llm": {"l2": false, "info": "", "list": false, "name": "llm", "type": "BaseLLM", "l2_name": "llm", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "LLM"}, "_type": "ElasticKeywordsSearch", "prompt": {"l2": false, "info": "", "list": false, "name": "prompt", "type": "BasePromptTemplate", "l2_name": "prompt", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "prompt"}, "documents": {"l2": false, "info": "", "list": true, "name": "documents", "show": true, "type": "Document", "l2_name": "documents", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Documents"}, "embedding": {"l2": false, "info": "", "list": false, "name": "embedding", "show": false, "type": "Embeddings", "l2_name": "embedding", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Embedding"}, "metadatas": {"l2": false, "info": "", "list": true, "name": "metadatas", "show": false, "type": "code", "l2_name": "metadatas", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "index_name": {"l2": true, "info": "", "list": false, "name": "index_name", "show": true, "type": "str", "value": "col_1700798314_e3f656ab", "l2_name": "index_name", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "ssl_verify": {"l2": false, "info": "", "list": false, "name": "ssl_verify", "show": true, "type": "str", "value": "", "l2_name": "ssl_verify", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "ssl_verify"}, "search_kwargs": {"l2": false, "info": "", "list": false, "name": "search_kwargs", "show": true, "type": "code", "value": "{}", "l2_name": "search_kwargs", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "refresh_indices": {"l2": false, "info": "", "list": false, "name": "refresh_indices", "show": false, "type": "bool", "value": true, "l2_name": "refresh_indices", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "elasticsearch_url": {"l2": false, "info": "", "list": false, "name": "elasticsearch_url", "show": true, "type": "str", "value": "", "l2_name": "elasticsearch_url", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "ES_connection_url"}}, "description": "Construct ElasticKeywordsSearch wrapper from raw documents.", "base_classes": ["VectorStore", "ElasticKeywordsSearch", "BaseRetriever", "VectorStoreRetriever"], "display_name": "ElasticKeywordsSearch", "output_types": [], "custom_fields": {}, "documentation": "http://***************:8030"}, "type": "ElasticKeywordsSearch", "value": null}, "selected": true, "positionAbsolute": {"x": -3007.9415392561414, "y": -220.71638201134618}}, {"width": 384, "height": 494, "id": "ConversationalRetrievalChain-SmY3w", "type": "genericNode", "position": {"x": -1547.0269680478611, "y": -543.0897871904267}, "data": {"id": "ConversationalRetrievalChain-SmY3w", "node": {"template": {"llm": {"l2": false, "info": "", "list": false, "name": "llm", "show": true, "type": "BaseLanguageModel", "l2_name": "llm", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}, "_type": "ConversationalRetrievalChain", "memory": {"l2": false, "info": "", "list": false, "name": "memory", "show": true, "type": "BaseChatMemory", "l2_name": "memory", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}, "verbose": {"l2": false, "info": "", "list": false, "name": "verbose", "show": false, "type": "bool", "value": false, "l2_name": "verbose", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "callbacks": {"l2": false, "info": "", "list": false, "name": "callbacks", "show": false, "type": "Callbacks", "l2_name": "callbacks", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "retriever": {"l2": false, "info": "", "list": false, "name": "retriever", "show": true, "type": "BaseRetriever", "l2_name": "retriever", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}, "chain_type": {"l2": false, "info": "", "list": true, "name": "chain_type", "show": true, "type": "str", "value": "stuff", "l2_name": "chain_type", "options": ["stuff", "map_reduce", "map_rerank", "refine"], "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}, "input_node": {"l2": false, "info": "", "list": false, "name": "input_node", "show": true, "type": "input", "l2_name": "input_node", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Preset Question"}, "condense_question_llm": {"l2": false, "info": "", "list": false, "name": "condense_question_llm", "show": false, "type": "BaseLanguageModel", "l2_name": "condense_question_llm", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "return_source_documents": {"l2": false, "info": "", "list": false, "name": "return_source_documents", "show": true, "type": "bool", "value": true, "l2_name": "return_source_documents", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Return source documents"}, "condense_question_prompt": {"l2": false, "info": "", "list": false, "name": "condense_question_prompt", "show": true, "type": "BasePromptTemplate", "value": {"_type": "prompt", "template": "Given the following conversation and a follow up question, rephrase the follow up question to be a standalone question, in its original language.\n\nChat History:\n{chat_history}\nFollow Up Input: {question}\nStandalone question:", "output_parser": null, "input_variables": ["chat_history", "question"], "template_format": "f-string", "partial_variables": {}, "validate_template": true}, "l2_name": "condense_question_prompt", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "combine_docs_chain_kwargs": {"l2": false, "info": "", "list": false, "name": "combine_docs_chain_kwargs", "show": true, "type": "BasePromptTemplate", "l2_name": "combine_docs_chain_kwargs", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "prompt"}}, "description": "Convenience method to load chain from LLM and retriever.", "base_classes": ["BaseConversationalRetrievalChain", "Chain", "ConversationalRetrievalChain", "function"], "display_name": "ConversationalRetrievalChain", "output_types": [], "custom_fields": {}, "documentation": "https://python.langchain.com/docs/modules/chains/popular/chat_vector_db"}, "type": "ConversationalRetrievalChain", "value": null}, "selected": true, "positionAbsolute": {"x": -1547.0269680478611, "y": -543.0897871904267}}, {"width": 384, "height": 526, "id": "ConversationBufferMemory-K1tss", "type": "genericNode", "position": {"x": -2419.6151156542605, "y": -708.8327872944353}, "data": {"id": "ConversationBufferMemory-K1tss", "node": {"template": {"_type": "ConversationBufferMemory", "ai_prefix": {"l2": false, "info": "", "list": false, "name": "ai_prefix", "show": false, "type": "str", "value": "AI", "l2_name": "ai_prefix", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "input_key": {"l2": false, "info": "The variable to be used as Chat Input when more than one variable is available.", "list": false, "name": "input_key", "show": true, "type": "str", "value": "", "l2_name": "input_key", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "memory_key": {"l2": false, "info": "", "list": false, "name": "memory_key", "show": true, "type": "str", "value": "chat_history", "l2_name": "memory_key", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "output_key": {"l2": false, "info": "The variable to be used as Chat Output (e.g. answer in a ConversationalRetrievalChain)", "list": false, "name": "output_key", "show": true, "type": "str", "value": "answer", "l2_name": "output_key", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "chat_memory": {"l2": false, "info": "", "list": false, "name": "chat_memory", "show": true, "type": "BaseChatMessageHistory", "l2_name": "chat_memory", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "human_prefix": {"l2": false, "info": "", "list": false, "name": "human_prefix", "show": false, "type": "str", "value": "Human", "l2_name": "human_prefix", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "return_messages": {"l2": false, "info": "", "list": false, "name": "return_messages", "show": true, "type": "bool", "l2_name": "return_messages", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}}, "description": "Buffer for storing conversation memory.", "base_classes": ["BaseMemory", "ConversationBufferMemory", "BaseChatMemory"], "display_name": "ConversationBufferMemory", "output_types": [], "custom_fields": {}, "documentation": "https://python.langchain.com/docs/modules/memory/how_to/buffer"}, "type": "ConversationBufferMemory", "value": null}, "selected": true, "positionAbsolute": {"x": -2419.6151156542605, "y": -708.8327872944353}}, {"width": 384, "height": 290, "id": "PromptTemplate-bs0vj", "type": "genericNode", "position": {"x": -2424.4899097749667, "y": -1236.9354837042674}, "data": {"id": "PromptTemplate-bs0vj", "node": {"name": "", "template": {"_type": "PromptTemplate", "template": {"l2": false, "info": "", "list": false, "name": "template", "show": true, "type": "prompt", "value": "参考文本：\n{context}\n\n----------------------\n\n1.用中文回答用户问题，并且答案要严谨专业。\n2.你需要依据以上【参考文本】中的内容来回答，当【参考文本】中有明确与用户问题相关的内容时才进行回答，不可根据自己的知识来回答。\n3.由于【参考文本】可能包含多个来自不同信息源的信息，所以根据这些不同的信息源可能得出有差异甚至冲突的答案，当发现这种情况时，这些答案都列举出来；如果没有冲突或差异，则只需要给出一个最终结果。\n4.若【参考文本】中内容与用户问题不相关则回复“没有找到相关内容”。\n\n----------------------\n\n用户问题: {question}\n你的回答:", "l2_name": "template", "advanced": false, "password": false, "required": true, "multiline": true, "placeholder": ""}, "output_parser": {"l2": false, "info": "", "list": false, "name": "output_parser", "show": true, "type": "BaseOutputParser", "l2_name": "output_parser", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "input_variables": {"l2": false, "info": "", "list": true, "name": "input_variables", "show": false, "type": "str", "value": ["context", "question"], "l2_name": "input_variables", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}, "template_format": {"l2": false, "info": "", "list": false, "name": "template_format", "show": false, "type": "str", "value": "f-string", "l2_name": "template_format", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "partial_variables": {"l2": false, "info": "", "list": false, "name": "partial_variables", "show": false, "type": "code", "l2_name": "partial_variables", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "validate_template": {"l2": false, "info": "", "list": false, "name": "validate_template", "show": false, "type": "bool", "value": true, "l2_name": "validate_template", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}}, "description": "<PERSON><PERSON><PERSON> to represent a prompt for an LLM.", "base_classes": ["BasePromptTemplate", "PromptTemplate", "StringPromptTemplate"], "display_name": "PromptTemplate", "output_types": [], "custom_fields": {"": ["context", "question"], "template": ["context", "question"]}, "documentation": "https://python.langchain.com/docs/modules/model_io/prompts/prompt_templates/", "field_formatters": {"formatters": {"openai_api_key": {}}, "base_formatters": {"dict": {}, "list": {}, "show": {}, "union": {}, "kwargs": {}, "default": {}, "headers": {}, "optional": {}, "password": {}, "multiline": {}, "model_fields": {"MODEL_DICT": {"OpenAI": ["text-davinci-003", "text-davinci-002", "text-curie-001", "text-babbage-001", "text-ada-001"], "Anthropic": ["claude-v1", "claude-v1-100k", "claude-instant-v1", "claude-instant-v1-100k", "claude-v1.3", "claude-v1.3-100k", "claude-v1.2", "claude-v1.0", "claude-instant-v1.1", "claude-instant-v1.1-100k", "claude-instant-v1.0"], "ChatOpenAI": ["gpt-3.5-turbo-0613", "gpt-3.5-turbo", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-16k", "gpt-4-0613", "gpt-4-32k-0613", "gpt-4", "gpt-4-32k"], "ChatAnthropic": ["claude-v1", "claude-v1-100k", "claude-instant-v1", "claude-instant-v1-100k", "claude-v1.3", "claude-v1.3-100k", "claude-v1.2", "claude-v1.0", "claude-instant-v1.1", "claude-instant-v1.1-100k", "claude-instant-v1.0"]}}, "dict_code_file": {}}}}, "type": "PromptTemplate", "value": null}, "selected": true, "positionAbsolute": {"x": -2424.4899097749667, "y": -1236.9354837042674}}], "edges": [{"source": "Milvus-cyR5W", "target": "MixEsVectorRetriever-J35CZ", "sourceHandle": "Milvus|Milvus-cyR5W|VectorStore|Milvus|BaseRetriever|VectorStoreRetriever", "targetHandle": "BaseRetriever|vector_retriever|MixEsVectorRetriever-J35CZ", "id": "reactflow__edge-Milvus-cyR5WMilvus|Milvus-cyR5W|VectorStore|Milvus|BaseRetriever|VectorStoreRetriever-MixEsVectorRetriever-J35CZBaseRetriever|vector_retriever|MixEsVectorRetriever-J35CZ", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "ElasticKeywordsSearch-31Et9", "target": "MixEsVectorRetriever-J35CZ", "sourceHandle": "ElasticKeywordsSearch|ElasticKeywordsSearch-31Et9|VectorStore|ElasticKeywordsSearch|BaseRetriever|VectorStoreRetriever", "targetHandle": "BaseRetriever|keyword_retriever|MixEsVectorRetriever-J35CZ", "id": "reactflow__edge-ElasticKeywordsSearch-31Et9ElasticKeywordsSearch|ElasticKeywordsSearch-31Et9|VectorStore|ElasticKeywordsSearch|BaseRetriever|VectorStoreRetriever-MixEsVectorRetriever-J35CZBaseRetriever|keyword_retriever|MixEsVectorRetriever-J35CZ", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "MixEsVectorRetriever-J35CZ", "target": "ConversationalRetrievalChain-SmY3w", "sourceHandle": "MixEsVectorRetriever|MixEsVectorRetriever-J35CZ|BaseRetriever|MixEsVectorRetriever", "targetHandle": "BaseRetriever|retriever|ConversationalRetrievalChain-SmY3w", "id": "reactflow__edge-MixEsVectorRetriever-J35CZMixEsVectorRetriever|MixEsVectorRetriever-J35CZ|BaseRetriever|MixEsVectorRetriever-ConversationalRetrievalChain-SmY3wBaseRetriever|retriever|ConversationalRetrievalChain-SmY3w", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "ProxyChatLLM-UYoB4", "target": "ConversationalRetrievalChain-SmY3w", "sourceHandle": "ProxyChatLLM|ProxyChatLLM-UYoB4|ProxyChatLLM|BaseLanguageModel|BaseChatModel|BaseLLM", "targetHandle": "BaseLanguageModel|llm|ConversationalRetrievalChain-SmY3w", "id": "reactflow__edge-ProxyChatLLM-UYoB4ProxyChatLLM|ProxyChatLLM-UYoB4|ProxyChatLLM|BaseLanguageModel|BaseChatModel|BaseLLM-ConversationalRetrievalChain-SmY3wBaseLanguageModel|llm|ConversationalRetrievalChain-SmY3w", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "ConversationBufferMemory-K1tss", "target": "ConversationalRetrievalChain-SmY3w", "sourceHandle": "ConversationBufferMemory|ConversationBufferMemory-K1tss|BaseMemory|ConversationBufferMemory|BaseChatMemory", "targetHandle": "BaseChatMemory|memory|ConversationalRetrievalChain-SmY3w", "id": "reactflow__edge-ConversationBufferMemory-K1tssConversationBufferMemory|ConversationBufferMemory-K1tss|BaseMemory|ConversationBufferMemory|BaseChatMemory-ConversationalRetrievalChain-SmY3wBaseChatMemory|memory|ConversationalRetrievalChain-SmY3w", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "PromptTemplate-bs0vj", "target": "ConversationalRetrievalChain-SmY3w", "sourceHandle": "PromptTemplate|PromptTemplate-bs0vj|BasePromptTemplate|PromptTemplate|StringPromptTemplate", "targetHandle": "BasePromptTemplate|combine_docs_chain_kwargs|ConversationalRetrievalChain-SmY3w", "id": "reactflow__edge-PromptTemplate-bs0vjPromptTemplate|PromptTemplate-bs0vj|BasePromptTemplate|PromptTemplate|StringPromptTemplate-ConversationalRetrievalChain-SmY3wBasePromptTemplate|combine_docs_chain_kwargs|ConversationalRetrievalChain-SmY3w", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}], "viewport": {"zoom": 1, "x": 0, "y": 0}}, "is_component": false, "name": "", "description": "", "id": "89297", "status": 0, "write": false, "guide_word": ""}}}}, {"知识库问答": {"id": "RetrievalQA-527fa", "type": "RetrievalQA", "node": {"output_types": [], "display_name": "知识库问答", "documentation": "", "base_classes": ["RetrievalQA", "Chain", "BaseRetrievalQA", "function"], "description": "Chain for question-answering against an index.", "template": {"drop_old_Milvus-CWpae": {"l2": false, "info": "", "list": false, "name": "drop_old", "show": false, "type": "bool", "value": false, "l2_name": "drop_old", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-CWpae", "field": "drop_old"}, "display_name": "Drop Old"}, "documents_Milvus-CWpae": {"l2": false, "info": "", "list": true, "name": "documents", "show": true, "type": "Document", "l2_name": "documents", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Documents", "proxy": {"id": "Milvus-CWpae", "field": "documents"}}, "embedding_Milvus-CWpae": {"l2": false, "info": "", "list": false, "name": "embedding", "show": false, "type": "Embeddings", "l2_name": "embedding", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Embedding", "proxy": {"id": "Milvus-CWpae", "field": "embedding"}}, "metadatas_Milvus-CWpae": {"l2": false, "info": "", "list": true, "name": "metadatas", "show": false, "type": "code", "l2_name": "metadatas", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-CWpae", "field": "metadatas"}, "display_name": "Metadatas"}, "index_params_Milvus-CWpae": {"l2": false, "info": "", "list": false, "name": "index_params", "show": false, "type": "code", "l2_name": "index_params", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-CWpae", "field": "index_params"}, "display_name": "Index Params"}, "search_kwargs_Milvus-CWpae": {"l2": false, "info": "", "list": false, "name": "search_kwargs", "show": false, "type": "code", "value": "{}", "l2_name": "search_kwargs", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-CWpae", "field": "search_kwargs"}, "display_name": "Search Kwargs"}, "search_params_Milvus-CWpae": {"l2": false, "info": "", "list": false, "name": "search_params", "show": false, "type": "code", "l2_name": "search_params", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-CWpae", "field": "search_params"}, "display_name": "Search Params"}, "collection_name_Milvus-CWpae": {"l2": true, "info": "", "list": false, "name": "collection_name", "show": true, "type": "str", "value": "毕昇文档20240309", "l2_name": "选择知识库", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "collection_id": 3520, "proxy": {"id": "Milvus-CWpae", "field": "collection_name"}, "display_name": "Collection Name"}, "connection_args_Milvus-CWpae": {"l2": false, "info": "", "list": false, "name": "connection_args", "show": false, "type": "code", "value": "", "l2_name": "connection_args", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-CWpae", "field": "connection_args"}, "display_name": "Connection Args"}, "consistency_level_Milvus-CWpae": {"l2": false, "info": "", "list": false, "name": "consistency_level", "show": false, "type": "str", "value": "Session", "l2_name": "consistency_level", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-CWpae", "field": "consistency_level"}, "display_name": "Consistency Level"}, "tags_RetrievalQA-x5nap": {"l2": false, "info": "", "list": true, "name": "tags", "show": false, "type": "str", "l2_name": "tags", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-x5nap", "field": "tags"}, "display_name": "Tags"}, "memory_RetrievalQA-x5nap": {"l2": false, "info": "", "list": false, "name": "memory", "show": true, "type": "BaseMemory", "l2_name": "memory", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-x5nap", "field": "memory"}, "display_name": "Memory"}, "verbose_RetrievalQA-x5nap": {"l2": false, "info": "", "list": false, "name": "verbose", "show": false, "type": "bool", "l2_name": "verbose", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-x5nap", "field": "verbose"}, "display_name": "Verbose"}, "metadata_RetrievalQA-x5nap": {"l2": false, "info": "", "list": false, "name": "metadata", "show": false, "type": "code", "l2_name": "metadata", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-x5nap", "field": "metadata"}, "display_name": "<PERSON><PERSON><PERSON>"}, "callbacks_RetrievalQA-x5nap": {"l2": false, "info": "", "list": true, "name": "callbacks", "show": false, "type": "langchain.callbacks.base.BaseCallbackHandler", "l2_name": "callbacks", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-x5nap", "field": "callbacks"}, "display_name": "Callbacks"}, "input_key_RetrievalQA-x5nap": {"l2": false, "info": "", "list": false, "name": "input_key", "show": true, "type": "str", "value": "query", "l2_name": "input_key", "advanced": true, "password": false, "required": true, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-x5nap", "field": "input_key"}, "display_name": "Input Key"}, "input_node_RetrievalQA-x5nap": {"l2": false, "info": "", "list": false, "name": "input_node", "show": true, "type": "input", "l2_name": "input_node", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Preset Question", "proxy": {"id": "RetrievalQA-x5nap", "field": "input_node"}}, "output_key_RetrievalQA-x5nap": {"l2": false, "info": "", "list": false, "name": "output_key", "show": true, "type": "str", "value": "result", "l2_name": "output_key", "advanced": true, "password": false, "required": true, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-x5nap", "field": "output_key"}, "display_name": "Output Key"}, "return_source_documents_RetrievalQA-x5nap": {"l2": false, "info": "", "list": false, "name": "return_source_documents", "show": true, "type": "bool", "value": true, "l2_name": "return_source_documents", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-x5nap", "field": "return_source_documents"}, "display_name": "Return Source Documents"}, "n_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "n", "show": false, "type": "int", "value": 1, "l2_name": "n", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "n"}, "display_name": "N"}, "tags_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": true, "name": "tags", "show": false, "type": "str", "l2_name": "tags", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "tags"}, "display_name": "Tags"}, "cache_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "cache", "show": false, "type": "bool", "l2_name": "cache", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "cache"}, "display_name": "<PERSON><PERSON>"}, "top_p_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "top_p", "show": true, "type": "float", "value": 0.9, "l2_name": "top_p", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "top_p"}, "display_name": "Top P"}, "client_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "client", "show": false, "type": "Any", "l2_name": "client", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "client"}, "display_name": "Client"}, "headers_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "headers", "show": false, "type": "code", "value": "", "l2_name": "headers", "advanced": true, "password": false, "required": false, "multiline": true, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "headers"}, "display_name": "Headers"}, "verbose_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "verbose", "show": false, "type": "bool", "value": false, "l2_name": "verbose", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "verbose"}, "display_name": "Verbose"}, "metadata_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "metadata", "show": false, "type": "code", "l2_name": "metadata", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "metadata"}, "display_name": "<PERSON><PERSON><PERSON>"}, "callbacks_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": true, "name": "callbacks", "show": false, "type": "langchain.callbacks.base.BaseCallbackHandler", "l2_name": "callbacks", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "callbacks"}, "display_name": "Callbacks"}, "streaming_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "streaming", "show": false, "type": "bool", "value": false, "l2_name": "streaming", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "streaming"}, "display_name": "Streaming"}, "max_tokens_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "max_tokens", "show": true, "type": "int", "value": "", "l2_name": "max_tokens", "advanced": true, "password": true, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "max_tokens"}, "display_name": "<PERSON>"}, "model_name_ProxyChatLLM-UYoB4": {"l2": true, "info": "", "list": false, "name": "model_name", "show": true, "type": "str", "value": "gpt-3.5-turbo-16k-0613", "l2_name": "填写模型名称（支持所有主流闭源模型）", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "model_name"}, "display_name": "Model Name"}, "max_retries_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "max_retries", "show": false, "type": "int", "value": 6, "l2_name": "max_retries", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "max_retries"}, "display_name": "Max Retries"}, "temperature_ProxyChatLLM-UYoB4": {"l2": true, "info": "", "list": false, "name": "temperature", "show": true, "type": "float", "value": 0.7, "l2_name": "温度（控制输出随机性）", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "temperature"}, "display_name": "Temperature"}, "model_kwargs_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "model_kwargs", "show": false, "type": "code", "l2_name": "model_kwargs", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "model_kwargs"}, "display_name": "Model Kwargs"}, "elemai_api_key_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "elemai_api_key", "show": true, "type": "str", "value": "", "l2_name": "elemai_api_key", "advanced": true, "password": true, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "elemai_api_key"}, "display_name": "Elemai Api Key"}, "elemai_base_url_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "elemai_base_url", "show": true, "type": "str", "value": "http://192.168.106.12:6001/v1/chatcompletion", "l2_name": "elemai_base_url", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "elemai_base_url"}, "display_name": "Elemai Base Url"}, "request_timeout_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "request_timeout", "show": false, "type": "float", "l2_name": "request_timeout", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "request_timeout"}, "display_name": "Request Timeout"}, "tiktoken_model_name_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "tiktoken_model_name", "show": false, "type": "str", "l2_name": "tiktoken_model_name", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "tiktoken_model_name"}, "display_name": "Tiktoken Model Name"}, "token_max_CombineDocsChain-71a85": {"info": "只对Stuff类型生效", "list": false, "name": "token_max", "show": true, "type": "int", "value": -1, "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "token_max", "proxy": {"id": "CombineDocsChain-71a85", "field": "token_max"}}, "chain_type_CombineDocsChain-71a85": {"info": "", "list": true, "name": "chain_type", "show": true, "type": "str", "value": "stuff", "options": ["stuff", "map_reduce", "map_rerank", "refine"], "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "proxy": {"id": "CombineDocsChain-71a85", "field": "chain_type"}, "display_name": "Chain Type"}, "source_PromptTemplate-3c164": {"info": "", "list": false, "name": "source", "show": true, "type": "str", "value": "", "advanced": false, "password": false, "required": false, "multiline": true, "input_types": ["Document", "BaseOutputParser", "VariableNode"], "placeholder": "", "display_name": "source", "proxy": {"id": "PromptTemplate-3c164", "field": "source"}}, "template_PromptTemplate-3c164": {"l2": false, "info": "", "list": false, "name": "template", "show": true, "type": "prompt", "value": "title: {source}\n{page_content}\n----------------", "l2_name": "提示词模板", "advanced": false, "password": false, "required": true, "multiline": true, "placeholder": "", "proxy": {"id": "PromptTemplate-3c164", "field": "template"}, "display_name": "Template"}, "input_types_PromptTemplate-3c164": {"info": "", "list": false, "name": "input_types", "show": false, "type": "code", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-3c164", "field": "input_types"}, "display_name": "Input Types"}, "page_content_PromptTemplate-3c164": {"info": "", "list": false, "name": "page_content", "show": true, "type": "str", "value": "", "advanced": false, "password": false, "required": false, "multiline": true, "input_types": ["Document", "BaseOutputParser", "VariableNode"], "placeholder": "", "display_name": "page_content", "proxy": {"id": "PromptTemplate-3c164", "field": "page_content"}}, "output_parser_PromptTemplate-3c164": {"info": "", "list": false, "name": "output_parser", "show": true, "type": "BaseOutputParser", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-3c164", "field": "output_parser"}, "display_name": "Output Parser"}, "input_variables_PromptTemplate-3c164": {"info": "", "list": true, "name": "input_variables", "show": false, "type": "str", "value": ["source", "page_content"], "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-3c164", "field": "input_variables"}, "display_name": "Input Variables"}, "template_format_PromptTemplate-3c164": {"info": "", "list": false, "name": "template_format", "show": false, "type": "Union[Literal['f-string'], Literal['jinja2']]", "value": "f-string", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-3c164", "field": "template_format"}, "display_name": "Template Format"}, "partial_variables_PromptTemplate-3c164": {"info": "", "list": false, "name": "partial_variables", "show": false, "type": "code", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-3c164", "field": "partial_variables"}, "display_name": "Partial Variables"}, "validate_template_PromptTemplate-3c164": {"info": "", "list": false, "name": "validate_template", "show": false, "type": "bool", "value": false, "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-3c164", "field": "validate_template"}, "display_name": "Validate Template"}, "context_PromptTemplate-ced29": {"info": "", "list": false, "name": "context", "show": true, "type": "str", "value": "", "advanced": false, "password": false, "required": false, "multiline": true, "input_types": ["Document", "BaseOutputParser", "VariableNode"], "placeholder": "", "display_name": "context", "proxy": {"id": "PromptTemplate-ced29", "field": "context"}}, "question_PromptTemplate-ced29": {"info": "", "list": false, "name": "question", "show": true, "type": "str", "value": "", "advanced": false, "password": false, "required": false, "multiline": true, "input_types": ["Document", "BaseOutputParser", "VariableNode"], "placeholder": "", "display_name": "question", "proxy": {"id": "PromptTemplate-ced29", "field": "question"}}, "template_PromptTemplate-ced29": {"l2": true, "info": "", "list": false, "name": "template", "show": true, "type": "prompt", "value": "参考文本：\n{context}\n\n----------------------\n# Role：企业客服\n\n# Background :\n你是一家名叫XX公司的客服人员。你的日常工作是回复公司内用户的各类咨询问题，所以当用户提到“公司”时，指的是XX公司。\n\n# Goals:\n- 以上【参考文本】中是从企业知识库中查找到的相关信息，只能结合以上信息进行回答，若以上内容为空或其中没有找到能回答【用户问题】的内容时，则回复“没有找到相关内容”，不能根据你自己的知识自己发挥。\n- 特别要注意区分【用户问题】与【参考文本】中不同的日期、人名、公司名这些关键信息。\n- 用中文回答问题，并且答案要严谨、专业、清晰、可读性好。\n- 拥有排版审美, 会利用序号, 缩进, 分隔线和换行符等等来美化信息排版。\n\n----------------------\n\n用户问题: {question}\n你的回答:", "l2_name": "提示词模板", "advanced": false, "password": false, "required": true, "multiline": true, "placeholder": "", "proxy": {"id": "PromptTemplate-ced29", "field": "template"}, "display_name": "Template"}, "input_types_PromptTemplate-ced29": {"info": "", "list": false, "name": "input_types", "show": false, "type": "code", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-ced29", "field": "input_types"}, "display_name": "Input Types"}, "output_parser_PromptTemplate-ced29": {"info": "", "list": false, "name": "output_parser", "show": true, "type": "BaseOutputParser", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-ced29", "field": "output_parser"}, "display_name": "Output Parser"}, "input_variables_PromptTemplate-ced29": {"info": "", "list": true, "name": "input_variables", "show": false, "type": "str", "value": ["context", "question"], "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-ced29", "field": "input_variables"}, "display_name": "Input Variables"}, "template_format_PromptTemplate-ced29": {"info": "", "list": false, "name": "template_format", "show": false, "type": "Union[Literal['f-string'], Literal['jinja2']]", "value": "f-string", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-ced29", "field": "template_format"}, "display_name": "Template Format"}, "partial_variables_PromptTemplate-ced29": {"info": "", "list": false, "name": "partial_variables", "show": false, "type": "code", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-ced29", "field": "partial_variables"}, "display_name": "Partial Variables"}, "validate_template_PromptTemplate-ced29": {"info": "", "list": false, "name": "validate_template", "show": false, "type": "bool", "value": false, "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-ced29", "field": "validate_template"}, "display_name": "Validate Template"}}, "flow": {"data": {"nodes": [{"width": 384, "height": 310, "id": "Milvus-CWpae", "type": "genericNode", "position": {"x": -2861.8694801119937, "y": -286.57134522726807}, "data": {"id": "Milvus-CWpae", "node": {"l2_name": "知识库", "template": {"_type": "<PERSON><PERSON><PERSON><PERSON>", "drop_old": {"l2": false, "info": "", "list": false, "name": "drop_old", "show": false, "type": "bool", "value": false, "l2_name": "drop_old", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "documents": {"l2": false, "info": "", "list": true, "name": "documents", "show": true, "type": "Document", "l2_name": "documents", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Documents"}, "embedding": {"l2": false, "info": "", "list": false, "name": "embedding", "show": false, "type": "Embeddings", "l2_name": "embedding", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Embedding"}, "metadatas": {"l2": false, "info": "", "list": true, "name": "metadatas", "show": false, "type": "code", "l2_name": "metadatas", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "index_params": {"l2": false, "info": "", "list": false, "name": "index_params", "show": false, "type": "code", "l2_name": "index_params", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "search_kwargs": {"l2": false, "info": "", "list": false, "name": "search_kwargs", "show": true, "type": "code", "value": "{}", "l2_name": "search_kwargs", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "search_params": {"l2": false, "info": "", "list": false, "name": "search_params", "show": false, "type": "code", "l2_name": "search_params", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "collection_name": {"l2": true, "info": "", "list": false, "name": "collection_name", "show": true, "type": "str", "value": "毕昇文档20240309", "l2_name": "选择知识库", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "collection_id": 3520}, "connection_args": {"l2": false, "info": "", "list": false, "name": "connection_args", "show": true, "type": "code", "value": "", "l2_name": "connection_args", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "consistency_level": {"l2": false, "info": "", "list": false, "name": "consistency_level", "show": false, "type": "str", "value": "Session", "l2_name": "consistency_level", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}}, "description": "Create a Milvus collection, indexes it with HNSW, and insert data.", "base_classes": ["<PERSON><PERSON><PERSON><PERSON>", "VectorStore", "BaseRetriever", "VectorStoreRetriever"], "display_name": "<PERSON><PERSON><PERSON><PERSON>", "output_types": [], "custom_fields": {}, "documentation": "http://***************:8030"}, "type": "<PERSON><PERSON><PERSON><PERSON>", "value": null}, "selected": true, "positionAbsolute": {"x": -2861.8694801119937, "y": -286.57134522726807}}, {"width": 384, "height": 316, "id": "RetrievalQA-x5nap", "type": "genericNode", "position": {"x": -2198.289678633326, "y": -664.9918874506891}, "data": {"id": "RetrievalQA-x5nap", "node": {"template": {"tags": {"l2": false, "info": "", "list": true, "name": "tags", "show": false, "type": "str", "l2_name": "tags", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "_type": "RetrievalQA", "memory": {"l2": false, "info": "", "list": false, "name": "memory", "show": true, "type": "BaseMemory", "l2_name": "memory", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "verbose": {"l2": false, "info": "", "list": false, "name": "verbose", "show": false, "type": "bool", "l2_name": "verbose", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "metadata": {"l2": false, "info": "", "list": false, "name": "metadata", "show": false, "type": "code", "l2_name": "metadata", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "callbacks": {"l2": false, "info": "", "list": true, "name": "callbacks", "show": false, "type": "langchain.callbacks.base.BaseCallbackHandler", "l2_name": "callbacks", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "input_key": {"l2": false, "info": "", "list": false, "name": "input_key", "show": true, "type": "str", "value": "query", "l2_name": "input_key", "advanced": true, "password": false, "required": true, "multiline": false, "placeholder": ""}, "retriever": {"l2": false, "info": "", "list": false, "name": "retriever", "show": true, "type": "BaseRetriever", "l2_name": "retriever", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}, "input_node": {"l2": false, "info": "", "list": false, "name": "input_node", "show": true, "type": "input", "l2_name": "input_node", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Preset Question"}, "output_key": {"l2": false, "info": "", "list": false, "name": "output_key", "show": true, "type": "str", "value": "result", "l2_name": "output_key", "advanced": true, "password": false, "required": true, "multiline": false, "placeholder": ""}, "combine_documents_chain": {"l2": false, "info": "", "list": false, "name": "combine_documents_chain", "show": true, "type": "BaseCombineDocumentsChain", "l2_name": "combine_documents_chain", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}, "return_source_documents": {"l2": false, "info": "", "list": false, "name": "return_source_documents", "show": true, "type": "bool", "value": true, "l2_name": "return_source_documents", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}}, "description": "Chain for question-answering against an index.", "base_classes": ["RetrievalQA", "Chain", "BaseRetrievalQA", "function"], "display_name": "RetrievalQA", "output_types": [], "custom_fields": {}, "documentation": "https://python.langchain.com/docs/modules/chains/popular/vector_db_qa"}, "type": "RetrievalQA", "value": null}, "selected": true, "positionAbsolute": {"x": -2198.289678633326, "y": -664.9918874506891}}, {"width": 384, "height": 500, "id": "ProxyChatLLM-UYoB4", "type": "genericNode", "position": {"x": -3490.7895099718235, "y": -440.7836760027338}, "data": {"id": "ProxyChatLLM-UYoB4", "node": {"l2_name": "模型配置", "template": {"n": {"l2": false, "info": "", "list": false, "name": "n", "show": false, "type": "int", "value": 1, "l2_name": "n", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "tags": {"l2": false, "info": "", "list": true, "name": "tags", "show": false, "type": "str", "l2_name": "tags", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "_type": "ProxyChatLLM", "cache": {"l2": false, "info": "", "list": false, "name": "cache", "show": false, "type": "bool", "l2_name": "cache", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "top_p": {"l2": false, "info": "", "list": false, "name": "top_p", "show": true, "type": "float", "value": 0.9, "l2_name": "top_p", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "client": {"l2": false, "info": "", "list": false, "name": "client", "show": false, "type": "Any", "l2_name": "client", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "headers": {"l2": false, "info": "", "list": false, "name": "headers", "show": false, "type": "code", "value": "", "l2_name": "headers", "advanced": false, "password": false, "required": false, "multiline": true, "placeholder": ""}, "verbose": {"l2": false, "info": "", "list": false, "name": "verbose", "show": false, "type": "bool", "value": false, "l2_name": "verbose", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "metadata": {"l2": false, "info": "", "list": false, "name": "metadata", "show": false, "type": "code", "l2_name": "metadata", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "callbacks": {"l2": false, "info": "", "list": true, "name": "callbacks", "show": false, "type": "langchain.callbacks.base.BaseCallbackHandler", "l2_name": "callbacks", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "streaming": {"l2": false, "info": "", "list": false, "name": "streaming", "show": false, "type": "bool", "value": false, "l2_name": "streaming", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "max_tokens": {"l2": false, "info": "", "list": false, "name": "max_tokens", "show": true, "type": "int", "value": "", "l2_name": "max_tokens", "advanced": true, "password": true, "required": false, "multiline": false, "placeholder": ""}, "model_name": {"l2": true, "info": "", "list": false, "name": "model_name", "show": true, "type": "str", "value": "gpt-3.5-turbo-16k-0613", "l2_name": "填写模型名称（支持所有主流闭源模型）", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "max_retries": {"l2": false, "info": "", "list": false, "name": "max_retries", "show": false, "type": "int", "value": 6, "l2_name": "max_retries", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "temperature": {"l2": true, "info": "", "list": false, "name": "temperature", "show": true, "type": "float", "value": 0.7, "l2_name": "温度（控制输出随机性）", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "model_kwargs": {"l2": false, "info": "", "list": false, "name": "model_kwargs", "show": true, "type": "code", "l2_name": "model_kwargs", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "elemai_api_key": {"l2": false, "info": "", "list": false, "name": "elemai_api_key", "show": true, "type": "str", "value": "", "l2_name": "elemai_api_key", "advanced": false, "password": true, "required": false, "multiline": false, "placeholder": ""}, "elemai_base_url": {"l2": false, "info": "", "list": false, "name": "elemai_base_url", "show": true, "type": "str", "value": "http://192.168.106.12:6001/v1/chatcompletion", "l2_name": "elemai_base_url", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "request_timeout": {"l2": false, "info": "", "list": false, "name": "request_timeout", "show": false, "type": "float", "l2_name": "request_timeout", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "tiktoken_model_name": {"l2": false, "info": "", "list": false, "name": "tiktoken_model_name", "show": false, "type": "str", "l2_name": "tiktoken_model_name", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}}, "description": "Wrapper around proxy Chat large language models.", "base_classes": ["ProxyChatLLM", "BaseLanguageModel", "BaseChatModel", "BaseLLM"], "display_name": "ProxyChatLLM", "output_types": [], "custom_fields": {}, "documentation": ""}, "type": "ProxyChatLLM", "value": null}, "selected": true, "positionAbsolute": {"x": -3490.7895099718235, "y": -440.7836760027338}}, {"width": 384, "height": 448, "id": "CombineDocsChain-71a85", "type": "genericNode", "position": {"x": -2872.4473624642733, "y": -941.8372718902958}, "data": {"id": "CombineDocsChain-71a85", "node": {"template": {"llm": {"info": "", "list": false, "name": "llm", "show": true, "type": "BaseLanguageModel", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "display_name": "LLM"}, "_type": "load_qa_chain", "prompt": {"info": "只对Stuff类型生效", "list": false, "name": "prompt", "show": true, "type": "BasePromptTemplate", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "prompt"}, "token_max": {"info": "只对Stuff类型生效", "list": false, "name": "token_max", "show": true, "type": "int", "value": -1, "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "token_max"}, "chain_type": {"info": "", "list": true, "name": "chain_type", "show": true, "type": "str", "value": "stuff", "options": ["stuff", "map_reduce", "map_rerank", "refine"], "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}, "document_prompt": {"info": "", "list": false, "name": "document_prompt", "show": true, "type": "BasePromptTemplate", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}}, "description": "Load question answering chain.", "base_classes": ["BaseCombineDocumentsChain", "function"], "display_name": "CombineDocsChain", "output_types": [], "custom_fields": {}, "documentation": ""}, "type": "CombineDocsChain", "value": null}, "selected": true, "positionAbsolute": {"x": -2872.4473624642733, "y": -941.8372718902958}}, {"width": 384, "height": 454, "id": "PromptTemplate-3c164", "type": "genericNode", "position": {"x": -3470.0105149154433, "y": -1425.3602052103988}, "data": {"id": "PromptTemplate-3c164", "node": {"name": "", "l2_name": "召回段落拼接提示词", "template": {"_type": "PromptTemplate", "source": {"info": "", "list": false, "name": "source", "show": true, "type": "str", "value": "", "advanced": false, "password": false, "required": false, "multiline": true, "input_types": ["Document", "BaseOutputParser", "VariableNode"], "placeholder": "", "display_name": "source"}, "template": {"l2": false, "info": "", "list": false, "name": "template", "show": true, "type": "prompt", "value": "title: {source}\n{page_content}\n----------------", "l2_name": "提示词模板", "advanced": false, "password": false, "required": true, "multiline": true, "placeholder": ""}, "input_types": {"info": "", "list": false, "name": "input_types", "show": false, "type": "code", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "page_content": {"info": "", "list": false, "name": "page_content", "show": true, "type": "str", "value": "", "advanced": false, "password": false, "required": false, "multiline": true, "input_types": ["Document", "BaseOutputParser", "VariableNode"], "placeholder": "", "display_name": "page_content"}, "output_parser": {"info": "", "list": false, "name": "output_parser", "show": true, "type": "BaseOutputParser", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "input_variables": {"info": "", "list": true, "name": "input_variables", "show": false, "type": "str", "value": ["source", "page_content"], "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}, "template_format": {"info": "", "list": false, "name": "template_format", "show": false, "type": "Union[Literal['f-string'], Literal['jinja2']]", "value": "f-string", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "partial_variables": {"info": "", "list": false, "name": "partial_variables", "show": false, "type": "code", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "validate_template": {"info": "", "list": false, "name": "validate_template", "show": false, "type": "bool", "value": false, "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}}, "description": "A prompt template for a language model.", "base_classes": ["Runnable", "StringPromptTemplate", "RunnableSerializable", "PromptTemplate", "Generic", "BasePromptTemplate"], "display_name": "PromptTemplate", "output_types": [], "custom_fields": {"": ["source", "page_content"]}, "documentation": "https://python.langchain.com/docs/modules/model_io/prompts/prompt_templates/", "field_formatters": {}}, "type": "PromptTemplate", "value": null}, "selected": true, "positionAbsolute": {"x": -3470.0105149154433, "y": -1425.3602052103988}}, {"width": 384, "height": 454, "id": "PromptTemplate-ced29", "type": "genericNode", "position": {"x": -3479.600564127818, "y": -925.8998699556519}, "data": {"id": "PromptTemplate-ced29", "node": {"name": "", "l2_name": "大模型问答提示词", "template": {"_type": "PromptTemplate", "context": {"info": "", "list": false, "name": "context", "show": true, "type": "str", "value": "", "advanced": false, "password": false, "required": false, "multiline": true, "input_types": ["Document", "BaseOutputParser", "VariableNode"], "placeholder": "", "display_name": "context"}, "question": {"info": "", "list": false, "name": "question", "show": true, "type": "str", "value": "", "advanced": false, "password": false, "required": false, "multiline": true, "input_types": ["Document", "BaseOutputParser", "VariableNode"], "placeholder": "", "display_name": "question"}, "template": {"l2": true, "info": "", "list": false, "name": "template", "show": true, "type": "prompt", "value": "参考文本：\n{context}\n\n----------------------\n# Role：企业客服\n\n# Background :\n你是一家名叫XX公司的客服人员。你的日常工作是回复公司内用户的各类咨询问题，所以当用户提到“公司”时，指的是XX公司。\n\n# Goals:\n- 以上【参考文本】中是从企业知识库中查找到的相关信息，只能结合以上信息进行回答，若以上内容为空或其中没有找到能回答【用户问题】的内容时，则回复“没有找到相关内容”，不能根据你自己的知识自己发挥。\n- 特别要注意区分【用户问题】与【参考文本】中不同的日期、人名、公司名这些关键信息。\n- 用中文回答问题，并且答案要严谨、专业、清晰、可读性好。\n- 拥有排版审美, 会利用序号, 缩进, 分隔线和换行符等等来美化信息排版。\n\n----------------------\n\n用户问题: {question}\n你的回答:", "l2_name": "提示词模板", "advanced": false, "password": false, "required": true, "multiline": true, "placeholder": ""}, "input_types": {"info": "", "list": false, "name": "input_types", "show": false, "type": "code", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "output_parser": {"info": "", "list": false, "name": "output_parser", "show": true, "type": "BaseOutputParser", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "input_variables": {"info": "", "list": true, "name": "input_variables", "show": false, "type": "str", "value": ["context", "question"], "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}, "template_format": {"info": "", "list": false, "name": "template_format", "show": false, "type": "Union[Literal['f-string'], Literal['jinja2']]", "value": "f-string", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "partial_variables": {"info": "", "list": false, "name": "partial_variables", "show": false, "type": "code", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "validate_template": {"info": "", "list": false, "name": "validate_template", "show": false, "type": "bool", "value": false, "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}}, "description": "A prompt template for a language model.", "base_classes": ["Runnable", "StringPromptTemplate", "RunnableSerializable", "PromptTemplate", "Generic", "BasePromptTemplate"], "display_name": "PromptTemplate", "output_types": [], "custom_fields": {"": ["context", "question"]}, "documentation": "https://python.langchain.com/docs/modules/model_io/prompts/prompt_templates/", "field_formatters": {}}, "type": "PromptTemplate", "value": null}, "selected": true, "positionAbsolute": {"x": -3479.600564127818, "y": -925.8998699556519}}], "edges": [{"source": "Milvus-CWpae", "target": "RetrievalQA-x5nap", "sourceHandle": "Milvus|Milvus-CWpae|Milvus|VectorStore|BaseRetriever|VectorStoreRetriever", "targetHandle": "BaseRetriever|retriever|RetrievalQA-x5nap", "id": "reactflow__edge-Milvus-CWpaeMilvus|Milvus-CWpae|Milvus|VectorStore|BaseRetriever|VectorStoreRetriever-RetrievalQA-x5napBaseRetriever|retriever|RetrievalQA-x5nap", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "ProxyChatLLM-UYoB4", "target": "CombineDocsChain-71a85", "sourceHandle": "ProxyChatLLM|ProxyChatLLM-UYoB4|ProxyChatLLM|BaseLanguageModel|BaseChatModel|BaseLLM", "targetHandle": "BaseLanguageModel|llm|CombineDocsChain-71a85", "id": "reactflow__edge-ProxyChatLLM-UYoB4ProxyChatLLM|ProxyChatLLM-UYoB4|ProxyChatLLM|BaseLanguageModel|BaseChatModel|BaseLLM-CombineDocsChain-71a85BaseLanguageModel|llm|CombineDocsChain-71a85", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "CombineDocsChain-71a85", "target": "RetrievalQA-x5nap", "sourceHandle": "CombineDocsChain|CombineDocsChain-71a85|BaseCombineDocumentsChain|function", "targetHandle": "BaseCombineDocumentsChain|combine_documents_chain|RetrievalQA-x5nap", "id": "reactflow__edge-CombineDocsChain-71a85CombineDocsChain|CombineDocsChain-71a85|BaseCombineDocumentsChain|function-RetrievalQA-x5napBaseCombineDocumentsChain|combine_documents_chain|RetrievalQA-x5nap", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "PromptTemplate-3c164", "target": "CombineDocsChain-71a85", "sourceHandle": "PromptTemplate|PromptTemplate-3c164|Runnable|StringPromptTemplate|RunnableSerializable|PromptTemplate|Generic|BasePromptTemplate", "targetHandle": "BasePromptTemplate|document_prompt|CombineDocsChain-71a85", "id": "reactflow__edge-PromptTemplate-3c164PromptTemplate|PromptTemplate-3c164|Runnable|StringPromptTemplate|RunnableSerializable|PromptTemplate|Generic|BasePromptTemplate-CombineDocsChain-71a85BasePromptTemplate|document_prompt|CombineDocsChain-71a85", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "PromptTemplate-ced29", "target": "CombineDocsChain-71a85", "sourceHandle": "PromptTemplate|PromptTemplate-ced29|Runnable|StringPromptTemplate|RunnableSerializable|PromptTemplate|Generic|BasePromptTemplate", "targetHandle": "BasePromptTemplate|prompt|CombineDocsChain-71a85", "id": "reactflow__edge-PromptTemplate-ced29PromptTemplate|PromptTemplate-ced29|Runnable|StringPromptTemplate|RunnableSerializable|PromptTemplate|Generic|BasePromptTemplate-CombineDocsChain-71a85BasePromptTemplate|prompt|CombineDocsChain-71a85", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}], "viewport": {"zoom": 1, "x": 0, "y": 0}}, "is_component": false, "name": "", "description": "", "id": "72f43", "status": 0, "write": false, "guide_word": ""}}}}, {"表格数据问答": {"id": "RetrievalQA-c61ba", "type": "InputFileNode", "node": {"output_types": [], "display_name": "表格数据问答", "documentation": "", "base_classes": ["RetrievalQA", "Chain", "BaseRetrievalQA", "function"], "description": "Chain for question-answering against an index.", "template": {"token_max_CombineDocsChain-RCPNA": {"l2": false, "info": "当前只对stuff 生效", "list": false, "name": "token_max", "show": true, "type": "int", "value": -1, "l2_name": "token_max", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "token_max", "proxy": {"id": "CombineDocsChain-RCPNA", "field": "token_max"}}, "chain_type_CombineDocsChain-RCPNA": {"l2": false, "info": "", "list": true, "name": "chain_type", "show": true, "type": "str", "value": "stuff", "l2_name": "chain_type", "options": ["stuff", "map_reduce", "map_rerank", "refine"], "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "proxy": {"id": "CombineDocsChain-RCPNA", "field": "chain_type"}, "display_name": "Chain Type"}, "drop_old_Milvus-CWpae": {"l2": false, "info": "", "list": false, "name": "drop_old", "show": false, "type": "bool", "value": false, "l2_name": "drop_old", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-CWpae", "field": "drop_old"}, "display_name": "Drop Old"}, "metadatas_Milvus-CWpae": {"l2": false, "info": "", "list": true, "name": "metadatas", "show": false, "type": "code", "l2_name": "metadatas", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-CWpae", "field": "metadatas"}, "display_name": "Metadatas"}, "index_params_Milvus-CWpae": {"l2": false, "info": "", "list": false, "name": "index_params", "show": false, "type": "code", "l2_name": "index_params", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-CWpae", "field": "index_params"}, "display_name": "Index Params"}, "search_kwargs_Milvus-CWpae": {"l2": false, "info": "", "list": false, "name": "search_kwargs", "show": false, "type": "code", "value": "{}", "l2_name": "search_kwargs", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-CWpae", "field": "search_kwargs"}, "display_name": "Search Kwargs"}, "search_params_Milvus-CWpae": {"l2": false, "info": "", "list": false, "name": "search_params", "show": false, "type": "code", "l2_name": "search_params", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-CWpae", "field": "search_params"}, "display_name": "Search Params"}, "collection_name_Milvus-CWpae": {"l2": false, "info": "", "list": false, "name": "collection_name", "show": true, "type": "str", "value": "", "l2_name": "选择知识库", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "collection_id": "", "proxy": {"id": "Milvus-CWpae", "field": "collection_name"}, "display_name": "Collection Name"}, "connection_args_Milvus-CWpae": {"l2": false, "info": "", "list": false, "name": "connection_args", "show": false, "type": "code", "value": "", "l2_name": "connection_args", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-CWpae", "field": "connection_args"}, "display_name": "Connection Args"}, "consistency_level_Milvus-CWpae": {"l2": false, "info": "", "list": false, "name": "consistency_level", "show": false, "type": "str", "value": "Session", "l2_name": "consistency_level", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-CWpae", "field": "consistency_level"}, "display_name": "Consistency Level"}, "tags_RetrievalQA-x5nap": {"l2": false, "info": "", "list": true, "name": "tags", "show": false, "type": "str", "l2_name": "tags", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-x5nap", "field": "tags"}, "display_name": "Tags"}, "memory_RetrievalQA-x5nap": {"l2": false, "info": "", "list": false, "name": "memory", "show": true, "type": "BaseMemory", "l2_name": "memory", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-x5nap", "field": "memory"}, "display_name": "Memory"}, "verbose_RetrievalQA-x5nap": {"l2": false, "info": "", "list": false, "name": "verbose", "show": false, "type": "bool", "l2_name": "verbose", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-x5nap", "field": "verbose"}, "display_name": "Verbose"}, "metadata_RetrievalQA-x5nap": {"l2": false, "info": "", "list": false, "name": "metadata", "show": false, "type": "code", "l2_name": "metadata", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-x5nap", "field": "metadata"}, "display_name": "<PERSON><PERSON><PERSON>"}, "callbacks_RetrievalQA-x5nap": {"l2": false, "info": "", "list": true, "name": "callbacks", "show": false, "type": "langchain.callbacks.base.BaseCallbackHandler", "l2_name": "callbacks", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-x5nap", "field": "callbacks"}, "display_name": "Callbacks"}, "input_key_RetrievalQA-x5nap": {"l2": false, "info": "", "list": false, "name": "input_key", "show": true, "type": "str", "value": "query", "l2_name": "input_key", "advanced": true, "password": false, "required": true, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-x5nap", "field": "input_key"}, "display_name": "Input Key"}, "input_node_RetrievalQA-x5nap": {"l2": false, "info": "", "list": false, "name": "input_node", "show": true, "type": "input", "l2_name": "input_node", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Preset Question", "proxy": {"id": "RetrievalQA-x5nap", "field": "input_node"}}, "output_key_RetrievalQA-x5nap": {"l2": false, "info": "", "list": false, "name": "output_key", "show": true, "type": "str", "value": "result", "l2_name": "output_key", "advanced": true, "password": false, "required": true, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-x5nap", "field": "output_key"}, "display_name": "Output Key"}, "return_source_documents_RetrievalQA-x5nap": {"l2": false, "info": "", "list": false, "name": "return_source_documents", "show": true, "type": "bool", "value": true, "l2_name": "return_source_documents", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-x5nap", "field": "return_source_documents"}, "display_name": "Return Source Documents"}, "n_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "n", "show": false, "type": "int", "value": 1, "l2_name": "n", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "n"}, "display_name": "N"}, "tags_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": true, "name": "tags", "show": false, "type": "str", "l2_name": "tags", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "tags"}, "display_name": "Tags"}, "cache_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "cache", "show": false, "type": "bool", "l2_name": "cache", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "cache"}, "display_name": "<PERSON><PERSON>"}, "top_p_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "top_p", "show": true, "type": "float", "value": 0.9, "l2_name": "top_p", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "top_p"}, "display_name": "Top P"}, "client_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "client", "show": false, "type": "Any", "l2_name": "client", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "client"}, "display_name": "Client"}, "headers_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "headers", "show": false, "type": "code", "value": "", "l2_name": "headers", "advanced": true, "password": false, "required": false, "multiline": true, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "headers"}, "display_name": "Headers"}, "verbose_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "verbose", "show": false, "type": "bool", "value": false, "l2_name": "verbose", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "verbose"}, "display_name": "Verbose"}, "metadata_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "metadata", "show": false, "type": "code", "l2_name": "metadata", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "metadata"}, "display_name": "<PERSON><PERSON><PERSON>"}, "callbacks_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": true, "name": "callbacks", "show": false, "type": "langchain.callbacks.base.BaseCallbackHandler", "l2_name": "callbacks", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "callbacks"}, "display_name": "Callbacks"}, "streaming_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "streaming", "show": false, "type": "bool", "value": false, "l2_name": "streaming", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "streaming"}, "display_name": "Streaming"}, "max_tokens_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "max_tokens", "show": true, "type": "int", "value": "", "l2_name": "max_tokens", "advanced": true, "password": true, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "max_tokens"}, "display_name": "<PERSON>"}, "model_name_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "model_name", "show": true, "type": "str", "value": "gpt-4", "l2_name": "model_name", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "model_name"}, "display_name": "Model Name"}, "max_retries_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "max_retries", "show": false, "type": "int", "value": 6, "l2_name": "max_retries", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "max_retries"}, "display_name": "Max Retries"}, "temperature_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "temperature", "show": true, "type": "float", "value": 0.7, "l2_name": "temperature", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "temperature"}, "display_name": "Temperature"}, "model_kwargs_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "model_kwargs", "show": false, "type": "code", "l2_name": "model_kwargs", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "model_kwargs"}, "display_name": "Model Kwargs"}, "elemai_api_key_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "elemai_api_key", "show": true, "type": "str", "value": "", "l2_name": "elemai_api_key", "advanced": true, "password": true, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "elemai_api_key"}, "display_name": "Elemai Api Key"}, "elemai_base_url_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "elemai_base_url", "show": true, "type": "str", "value": "http://192.168.106.12:6001/v1/chatcompletion", "l2_name": "elemai_base_url", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "elemai_base_url"}, "display_name": "Elemai Base Url"}, "request_timeout_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "request_timeout", "show": false, "type": "float", "l2_name": "request_timeout", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "request_timeout"}, "display_name": "Request Timeout"}, "tiktoken_model_name_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "tiktoken_model_name", "show": false, "type": "str", "l2_name": "tiktoken_model_name", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "tiktoken_model_name"}, "display_name": "Tiktoken Model Name"}, "proxy_url_OpenAIProxyEmbedding-1771b": {"info": "", "list": false, "name": "proxy_url", "show": false, "type": "str", "value": "", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "http://*************:8080", "proxy": {"id": "OpenAIProxyEmbedding-1771b", "field": "proxy_url"}, "display_name": "Proxy Url"}, "metadata_CSVLoader-854c0": {"info": "", "list": false, "name": "metadata", "show": false, "type": "code", "value": "{}", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "display_name": "<PERSON><PERSON><PERSON>", "proxy": {"id": "CSVLoader-854c0", "field": "metadata"}}, "file_path_InputFileNode-e4363": {"info": "", "list": false, "name": "file_path", "show": true, "type": "file", "value": "", "advanced": true, "password": false, "required": false, "suffixes": [".csv"], "fileTypes": ["csv"], "file_path": null, "multiline": false, "placeholder": "", "proxy": {"id": "InputFileNode-e4363", "field": "file_path"}, "display_name": "File Path"}, "file_type_InputFileNode-e4363": {"info": "Tips for which file should upload", "list": false, "name": "file_type", "show": true, "type": "str", "value": "CSV文件", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "提示上传文件类型", "display_name": "Name", "proxy": {"id": "InputFileNode-e4363", "field": "file_type"}}}, "flow": {"data": {"nodes": [{"width": 384, "height": 376, "id": "CombineDocsChain-RCPNA", "type": "genericNode", "position": {"x": 1039.4135881298942, "y": 116.97346377366182}, "data": {"id": "CombineDocsChain-RCPNA", "node": {"template": {"llm": {"l2": false, "info": "", "list": false, "name": "llm", "show": true, "type": "BaseLanguageModel", "l2_name": "llm", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "display_name": "LLM"}, "_type": "load_qa_chain", "token_max": {"l2": false, "info": "当前只对stuff 生效", "list": false, "name": "token_max", "show": true, "type": "int", "value": -1, "l2_name": "token_max", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "token_max"}, "chain_type": {"l2": false, "info": "", "list": true, "name": "chain_type", "show": true, "type": "str", "value": "stuff", "l2_name": "chain_type", "options": ["stuff", "map_reduce", "map_rerank", "refine"], "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}}, "description": "Load question answering chain.", "base_classes": ["BaseCombineDocumentsChain", "function"], "display_name": "CombineDocsChain", "output_types": [], "custom_fields": {}, "documentation": ""}, "type": "CombineDocsChain", "value": null}, "selected": true, "positionAbsolute": {"x": 1039.4135881298942, "y": 116.97346377366182}}, {"width": 384, "height": 346, "id": "Milvus-CWpae", "type": "genericNode", "position": {"x": 1052.1050968084205, "y": 659.5536672641022}, "data": {"id": "Milvus-CWpae", "node": {"l2_name": "知识库", "template": {"_type": "<PERSON><PERSON><PERSON><PERSON>", "drop_old": {"l2": false, "info": "", "list": false, "name": "drop_old", "show": false, "type": "bool", "value": false, "l2_name": "drop_old", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "documents": {"l2": false, "info": "", "list": true, "name": "documents", "show": true, "type": "Document", "l2_name": "documents", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Documents"}, "embedding": {"l2": false, "info": "", "list": false, "name": "embedding", "show": true, "type": "Embeddings", "l2_name": "embedding", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "display_name": "Embedding"}, "metadatas": {"l2": false, "info": "", "list": true, "name": "metadatas", "show": false, "type": "code", "l2_name": "metadatas", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "index_params": {"l2": false, "info": "", "list": false, "name": "index_params", "show": false, "type": "code", "l2_name": "index_params", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "search_kwargs": {"l2": false, "info": "", "list": false, "name": "search_kwargs", "show": true, "type": "code", "value": "{}", "l2_name": "search_kwargs", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "search_params": {"l2": false, "info": "", "list": false, "name": "search_params", "show": false, "type": "code", "l2_name": "search_params", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "collection_name": {"l2": false, "info": "", "list": false, "name": "collection_name", "show": true, "type": "str", "value": "", "l2_name": "选择知识库", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "collection_id": ""}, "connection_args": {"l2": false, "info": "", "list": false, "name": "connection_args", "show": true, "type": "code", "value": "", "l2_name": "connection_args", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "consistency_level": {"l2": false, "info": "", "list": false, "name": "consistency_level", "show": false, "type": "str", "value": "Session", "l2_name": "consistency_level", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}}, "description": "Create a Milvus collection, indexes it with HNSW, and insert data.", "base_classes": ["<PERSON><PERSON><PERSON><PERSON>", "VectorStore", "BaseRetriever", "VectorStoreRetriever"], "display_name": "<PERSON><PERSON><PERSON><PERSON>", "output_types": [], "custom_fields": {}, "documentation": "http://***************:8030"}, "type": "<PERSON><PERSON><PERSON><PERSON>", "value": null}, "selected": true, "positionAbsolute": {"x": 1052.1050968084205, "y": 659.5536672641022}}, {"width": 384, "height": 316, "id": "RetrievalQA-x5nap", "type": "genericNode", "position": {"x": 1715.6848982870883, "y": 279.846110701463}, "data": {"id": "RetrievalQA-x5nap", "node": {"template": {"tags": {"l2": false, "info": "", "list": true, "name": "tags", "show": false, "type": "str", "l2_name": "tags", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "_type": "RetrievalQA", "memory": {"l2": false, "info": "", "list": false, "name": "memory", "show": true, "type": "BaseMemory", "l2_name": "memory", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "verbose": {"l2": false, "info": "", "list": false, "name": "verbose", "show": false, "type": "bool", "l2_name": "verbose", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "metadata": {"l2": false, "info": "", "list": false, "name": "metadata", "show": false, "type": "code", "l2_name": "metadata", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "callbacks": {"l2": false, "info": "", "list": true, "name": "callbacks", "show": false, "type": "langchain.callbacks.base.BaseCallbackHandler", "l2_name": "callbacks", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "input_key": {"l2": false, "info": "", "list": false, "name": "input_key", "show": true, "type": "str", "value": "query", "l2_name": "input_key", "advanced": true, "password": false, "required": true, "multiline": false, "placeholder": ""}, "retriever": {"l2": false, "info": "", "list": false, "name": "retriever", "show": true, "type": "BaseRetriever", "l2_name": "retriever", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}, "input_node": {"l2": false, "info": "", "list": false, "name": "input_node", "show": true, "type": "input", "l2_name": "input_node", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Preset Question"}, "output_key": {"l2": false, "info": "", "list": false, "name": "output_key", "show": true, "type": "str", "value": "result", "l2_name": "output_key", "advanced": true, "password": false, "required": true, "multiline": false, "placeholder": ""}, "combine_documents_chain": {"l2": false, "info": "", "list": false, "name": "combine_documents_chain", "show": true, "type": "BaseCombineDocumentsChain", "l2_name": "combine_documents_chain", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}, "return_source_documents": {"l2": false, "info": "", "list": false, "name": "return_source_documents", "show": true, "type": "bool", "value": true, "l2_name": "return_source_documents", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}}, "description": "Chain for question-answering against an index.", "base_classes": ["RetrievalQA", "Chain", "BaseRetrievalQA", "function"], "display_name": "RetrievalQA", "output_types": [], "custom_fields": {}, "documentation": "https://python.langchain.com/docs/modules/chains/popular/vector_db_qa"}, "type": "RetrievalQA", "value": null}, "selected": true, "positionAbsolute": {"x": 1715.6848982870883, "y": 279.846110701463}}, {"width": 384, "height": 500, "id": "ProxyChatLLM-UYoB4", "type": "genericNode", "position": {"x": 437.5688719308881, "y": 20}, "data": {"id": "ProxyChatLLM-UYoB4", "node": {"template": {"n": {"l2": false, "info": "", "list": false, "name": "n", "show": false, "type": "int", "value": 1, "l2_name": "n", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "tags": {"l2": false, "info": "", "list": true, "name": "tags", "show": false, "type": "str", "l2_name": "tags", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "_type": "ProxyChatLLM", "cache": {"l2": false, "info": "", "list": false, "name": "cache", "show": false, "type": "bool", "l2_name": "cache", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "top_p": {"l2": false, "info": "", "list": false, "name": "top_p", "show": true, "type": "float", "value": 0.9, "l2_name": "top_p", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "client": {"l2": false, "info": "", "list": false, "name": "client", "show": false, "type": "Any", "l2_name": "client", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "headers": {"l2": false, "info": "", "list": false, "name": "headers", "show": false, "type": "code", "value": "", "l2_name": "headers", "advanced": false, "password": false, "required": false, "multiline": true, "placeholder": ""}, "verbose": {"l2": false, "info": "", "list": false, "name": "verbose", "show": false, "type": "bool", "value": false, "l2_name": "verbose", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "metadata": {"l2": false, "info": "", "list": false, "name": "metadata", "show": false, "type": "code", "l2_name": "metadata", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "callbacks": {"l2": false, "info": "", "list": true, "name": "callbacks", "show": false, "type": "langchain.callbacks.base.BaseCallbackHandler", "l2_name": "callbacks", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "streaming": {"l2": false, "info": "", "list": false, "name": "streaming", "show": false, "type": "bool", "value": false, "l2_name": "streaming", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "max_tokens": {"l2": false, "info": "", "list": false, "name": "max_tokens", "show": true, "type": "int", "value": "", "l2_name": "max_tokens", "advanced": true, "password": true, "required": false, "multiline": false, "placeholder": ""}, "model_name": {"l2": false, "info": "", "list": false, "name": "model_name", "show": true, "type": "str", "value": "gpt-4", "l2_name": "model_name", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "max_retries": {"l2": false, "info": "", "list": false, "name": "max_retries", "show": false, "type": "int", "value": 6, "l2_name": "max_retries", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "temperature": {"l2": false, "info": "", "list": false, "name": "temperature", "show": true, "type": "float", "value": 0.7, "l2_name": "temperature", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "model_kwargs": {"l2": false, "info": "", "list": false, "name": "model_kwargs", "show": true, "type": "code", "l2_name": "model_kwargs", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "elemai_api_key": {"l2": false, "info": "", "list": false, "name": "elemai_api_key", "show": true, "type": "str", "value": "", "l2_name": "elemai_api_key", "advanced": false, "password": true, "required": false, "multiline": false, "placeholder": ""}, "elemai_base_url": {"l2": false, "info": "", "list": false, "name": "elemai_base_url", "show": true, "type": "str", "value": "http://192.168.106.12:6001/v1/chatcompletion", "l2_name": "elemai_base_url", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "request_timeout": {"l2": false, "info": "", "list": false, "name": "request_timeout", "show": false, "type": "float", "l2_name": "request_timeout", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "tiktoken_model_name": {"l2": false, "info": "", "list": false, "name": "tiktoken_model_name", "show": false, "type": "str", "l2_name": "tiktoken_model_name", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}}, "description": "Wrapper around proxy Chat large language models.", "base_classes": ["ProxyChatLLM", "BaseLanguageModel", "BaseChatModel", "BaseLLM"], "display_name": "ProxyChatLLM", "output_types": [], "custom_fields": {}, "documentation": ""}, "type": "ProxyChatLLM", "value": null}, "selected": true, "positionAbsolute": {"x": 437.5688719308881, "y": 20}}, {"width": 384, "height": 172, "id": "OpenAIProxyEmbedding-1771b", "type": "genericNode", "position": {"x": 540.1525954325598, "y": 1040.7357784004414}, "data": {"id": "OpenAIProxyEmbedding-1771b", "node": {"template": {"_type": "proxy_embedding", "proxy_url": {"info": "", "list": false, "name": "proxy_url", "show": false, "type": "str", "value": "", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "http://*************:8080"}}, "description": " 使用自建的embedding服务使用openai进行embed ", "base_classes": ["Embeddings"], "display_name": "OpenAIProxyEmbedding", "output_types": [], "custom_fields": {}, "documentation": ""}, "type": "OpenAIProxyEmbedding", "value": null}, "selected": true, "positionAbsolute": {"x": 540.1525954325598, "y": 1040.7357784004414}}, {"width": 384, "height": 290, "id": "CSVLoader-854c0", "type": "genericNode", "position": {"x": 521.107515963895, "y": 664.4805878718827}, "data": {"id": "CSVLoader-854c0", "node": {"template": {"_type": "CSVLoader", "metadata": {"info": "", "list": false, "name": "metadata", "show": true, "type": "code", "value": "{}", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "display_name": "<PERSON><PERSON><PERSON>"}, "file_path": {"info": "", "list": false, "name": "file_path", "show": true, "type": "fileNode", "value": "", "advanced": false, "password": false, "required": false, "suffixes": [".csv"], "fileTypes": ["csv"], "multiline": false, "placeholder": ""}}, "description": "Load a `CSV` file into a list of Documents.", "base_classes": ["Document"], "display_name": "CSVLoader", "output_types": ["Document"], "custom_fields": {}, "documentation": "https://python.langchain.com/docs/modules/data_connection/document_loaders/integrations/csv"}, "type": "CSVLoader", "value": null}, "selected": true, "positionAbsolute": {"x": 521.107515963895, "y": 664.4805878718827}}, {"width": 384, "height": 336, "id": "InputFileNode-e4363", "type": "genericNode", "position": {"x": 18, "y": 635.9129686688856}, "data": {"id": "InputFileNode-e4363", "node": {"template": {"_type": "InputFileNode", "file_path": {"info": "", "list": false, "name": "file_path", "show": true, "type": "file", "value": "", "advanced": false, "password": false, "required": false, "suffixes": [".csv"], "fileTypes": ["csv"], "file_path": null, "multiline": false, "placeholder": ""}, "file_type": {"info": "Tips for which file should upload", "list": false, "name": "file_type", "show": true, "type": "str", "value": "CSV文件", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "提示上传文件类型", "display_name": "Name"}}, "description": "输入节点，用来自动对接输入", "base_classes": ["fileNode"], "display_name": "InputFileNode", "output_types": [], "custom_fields": {}, "documentation": ""}, "type": "InputFileNode", "value": null}, "selected": true, "positionAbsolute": {"x": 18, "y": 635.9129686688856}}], "edges": [{"source": "CombineDocsChain-RCPNA", "target": "RetrievalQA-x5nap", "sourceHandle": "CombineDocsChain|CombineDocsChain-RCPNA|BaseCombineDocumentsChain|function", "targetHandle": "BaseCombineDocumentsChain|combine_documents_chain|RetrievalQA-x5nap", "id": "reactflow__edge-CombineDocsChain-RCPNACombineDocsChain|CombineDocsChain-RCPNA|BaseCombineDocumentsChain|function-RetrievalQA-x5napBaseCombineDocumentsChain|combine_documents_chain|RetrievalQA-x5nap", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "Milvus-CWpae", "target": "RetrievalQA-x5nap", "sourceHandle": "Milvus|Milvus-CWpae|Milvus|VectorStore|BaseRetriever|VectorStoreRetriever", "targetHandle": "BaseRetriever|retriever|RetrievalQA-x5nap", "id": "reactflow__edge-Milvus-CWpaeMilvus|Milvus-CWpae|Milvus|VectorStore|BaseRetriever|VectorStoreRetriever-RetrievalQA-x5napBaseRetriever|retriever|RetrievalQA-x5nap", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "ProxyChatLLM-UYoB4", "target": "CombineDocsChain-RCPNA", "sourceHandle": "ProxyChatLLM|ProxyChatLLM-UYoB4|ProxyChatLLM|BaseLanguageModel|BaseChatModel|BaseLLM", "targetHandle": "BaseLanguageModel|llm|CombineDocsChain-RCPNA", "id": "reactflow__edge-ProxyChatLLM-UYoB4ProxyChatLLM|ProxyChatLLM-UYoB4|ProxyChatLLM|BaseLanguageModel|BaseChatModel|BaseLLM-CombineDocsChain-RCPNABaseLanguageModel|llm|CombineDocsChain-RCPNA", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "OpenAIProxyEmbedding-1771b", "target": "Milvus-CWpae", "sourceHandle": "OpenAIProxyEmbedding|OpenAIProxyEmbedding-1771b|Embeddings", "targetHandle": "Embeddings|embedding|Milvus-CWpae", "id": "reactflow__edge-OpenAIProxyEmbedding-1771bOpenAIProxyEmbedding|OpenAIProxyEmbedding-1771b|Embeddings-Milvus-CWpaeEmbeddings|embedding|Milvus-CWpae", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "CSVLoader-854c0", "target": "Milvus-CWpae", "sourceHandle": "CSVLoader|CSVLoader-854c0|Document", "targetHandle": "Document|documents|Milvus-CWpae", "id": "reactflow__edge-CSVLoader-854c0CSVLoader|CSVLoader-854c0|Document-Milvus-CWpaeDocument|documents|Milvus-CWpae", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "InputFileNode-e4363", "target": "CSVLoader-854c0", "sourceHandle": "InputFileNode|InputFileNode-e4363|fileNode", "targetHandle": "fileNode|file_path|CSVLoader-854c0", "id": "reactflow__edge-InputFileNode-e4363InputFileNode|InputFileNode-e4363|fileNode-CSVLoader-854c0fileNode|file_path|CSVLoader-854c0", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}], "viewport": {"zoom": 1, "x": 0, "y": 0}}, "is_component": false, "name": "", "description": "", "id": "7db0b", "status": 0, "write": false, "guide_word": ""}}}}, {"输入网址进行问答": {"id": "RetrievalQA-731c5", "type": "RetrievalQA", "node": {"output_types": [], "display_name": "输入网址进行问答", "documentation": "", "base_classes": ["RetrievalQA", "Chain", "BaseRetrievalQA", "function"], "description": "Chain for question-answering against an index.", "template": {"token_max_CombineDocsChain-RCPNA": {"l2": false, "info": "当前只对stuff 生效", "list": false, "name": "token_max", "show": true, "type": "int", "value": -1, "l2_name": "token_max", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "token_max", "proxy": {"id": "CombineDocsChain-RCPNA", "field": "token_max"}}, "chain_type_CombineDocsChain-RCPNA": {"l2": false, "info": "", "list": true, "name": "chain_type", "show": true, "type": "str", "value": "stuff", "l2_name": "chain_type", "options": ["stuff", "map_reduce", "map_rerank", "refine"], "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "proxy": {"id": "CombineDocsChain-RCPNA", "field": "chain_type"}, "display_name": "Chain Type"}, "drop_old_Milvus-CWpae": {"l2": false, "info": "", "list": false, "name": "drop_old", "show": false, "type": "bool", "value": false, "l2_name": "drop_old", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-CWpae", "field": "drop_old"}, "display_name": "Drop Old"}, "metadatas_Milvus-CWpae": {"l2": false, "info": "", "list": true, "name": "metadatas", "show": false, "type": "code", "l2_name": "metadatas", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-CWpae", "field": "metadatas"}, "display_name": "Metadatas"}, "index_params_Milvus-CWpae": {"l2": false, "info": "", "list": false, "name": "index_params", "show": false, "type": "code", "l2_name": "index_params", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-CWpae", "field": "index_params"}, "display_name": "Index Params"}, "search_kwargs_Milvus-CWpae": {"l2": false, "info": "", "list": false, "name": "search_kwargs", "show": false, "type": "code", "value": "{}", "l2_name": "search_kwargs", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-CWpae", "field": "search_kwargs"}, "display_name": "Search Kwargs"}, "search_params_Milvus-CWpae": {"l2": false, "info": "", "list": false, "name": "search_params", "show": false, "type": "code", "l2_name": "search_params", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-CWpae", "field": "search_params"}, "display_name": "Search Params"}, "collection_name_Milvus-CWpae": {"l2": false, "info": "", "list": false, "name": "collection_name", "show": true, "type": "str", "value": "", "l2_name": "选择知识库", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "collection_id": "", "proxy": {"id": "Milvus-CWpae", "field": "collection_name"}, "display_name": "Collection Name"}, "connection_args_Milvus-CWpae": {"l2": false, "info": "", "list": false, "name": "connection_args", "show": false, "type": "code", "value": "", "l2_name": "connection_args", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-CWpae", "field": "connection_args"}, "display_name": "Connection Args"}, "consistency_level_Milvus-CWpae": {"l2": false, "info": "", "list": false, "name": "consistency_level", "show": false, "type": "str", "value": "Session", "l2_name": "consistency_level", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "Milvus-CWpae", "field": "consistency_level"}, "display_name": "Consistency Level"}, "tags_RetrievalQA-x5nap": {"l2": false, "info": "", "list": true, "name": "tags", "show": false, "type": "str", "l2_name": "tags", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-x5nap", "field": "tags"}, "display_name": "Tags"}, "memory_RetrievalQA-x5nap": {"l2": false, "info": "", "list": false, "name": "memory", "show": true, "type": "BaseMemory", "l2_name": "memory", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-x5nap", "field": "memory"}, "display_name": "Memory"}, "verbose_RetrievalQA-x5nap": {"l2": false, "info": "", "list": false, "name": "verbose", "show": false, "type": "bool", "l2_name": "verbose", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-x5nap", "field": "verbose"}, "display_name": "Verbose"}, "metadata_RetrievalQA-x5nap": {"l2": false, "info": "", "list": false, "name": "metadata", "show": false, "type": "code", "l2_name": "metadata", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-x5nap", "field": "metadata"}, "display_name": "<PERSON><PERSON><PERSON>"}, "callbacks_RetrievalQA-x5nap": {"l2": false, "info": "", "list": true, "name": "callbacks", "show": false, "type": "langchain.callbacks.base.BaseCallbackHandler", "l2_name": "callbacks", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-x5nap", "field": "callbacks"}, "display_name": "Callbacks"}, "input_key_RetrievalQA-x5nap": {"l2": false, "info": "", "list": false, "name": "input_key", "show": true, "type": "str", "value": "query", "l2_name": "input_key", "advanced": true, "password": false, "required": true, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-x5nap", "field": "input_key"}, "display_name": "Input Key"}, "input_node_RetrievalQA-x5nap": {"l2": false, "info": "", "list": false, "name": "input_node", "show": true, "type": "input", "l2_name": "input_node", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Preset Question", "proxy": {"id": "RetrievalQA-x5nap", "field": "input_node"}}, "output_key_RetrievalQA-x5nap": {"l2": false, "info": "", "list": false, "name": "output_key", "show": true, "type": "str", "value": "result", "l2_name": "output_key", "advanced": true, "password": false, "required": true, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-x5nap", "field": "output_key"}, "display_name": "Output Key"}, "return_source_documents_RetrievalQA-x5nap": {"l2": false, "info": "", "list": false, "name": "return_source_documents", "show": true, "type": "bool", "value": true, "l2_name": "return_source_documents", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "RetrievalQA-x5nap", "field": "return_source_documents"}, "display_name": "Return Source Documents"}, "n_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "n", "show": false, "type": "int", "value": 1, "l2_name": "n", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "n"}, "display_name": "N"}, "tags_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": true, "name": "tags", "show": false, "type": "str", "l2_name": "tags", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "tags"}, "display_name": "Tags"}, "cache_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "cache", "show": false, "type": "bool", "l2_name": "cache", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "cache"}, "display_name": "<PERSON><PERSON>"}, "top_p_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "top_p", "show": true, "type": "float", "value": 0.9, "l2_name": "top_p", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "top_p"}, "display_name": "Top P"}, "client_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "client", "show": false, "type": "Any", "l2_name": "client", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "client"}, "display_name": "Client"}, "headers_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "headers", "show": false, "type": "code", "value": "", "l2_name": "headers", "advanced": true, "password": false, "required": false, "multiline": true, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "headers"}, "display_name": "Headers"}, "verbose_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "verbose", "show": false, "type": "bool", "value": false, "l2_name": "verbose", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "verbose"}, "display_name": "Verbose"}, "metadata_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "metadata", "show": false, "type": "code", "l2_name": "metadata", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "metadata"}, "display_name": "<PERSON><PERSON><PERSON>"}, "callbacks_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": true, "name": "callbacks", "show": false, "type": "langchain.callbacks.base.BaseCallbackHandler", "l2_name": "callbacks", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "callbacks"}, "display_name": "Callbacks"}, "streaming_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "streaming", "show": false, "type": "bool", "value": false, "l2_name": "streaming", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "streaming"}, "display_name": "Streaming"}, "max_tokens_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "max_tokens", "show": true, "type": "int", "value": "", "l2_name": "max_tokens", "advanced": true, "password": true, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "max_tokens"}, "display_name": "<PERSON>"}, "model_name_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "model_name", "show": true, "type": "str", "value": "gpt-4", "l2_name": "model_name", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "model_name"}, "display_name": "Model Name"}, "max_retries_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "max_retries", "show": false, "type": "int", "value": 6, "l2_name": "max_retries", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "max_retries"}, "display_name": "Max Retries"}, "temperature_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "temperature", "show": true, "type": "float", "value": 0.7, "l2_name": "temperature", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "temperature"}, "display_name": "Temperature"}, "model_kwargs_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "model_kwargs", "show": false, "type": "code", "l2_name": "model_kwargs", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "model_kwargs"}, "display_name": "Model Kwargs"}, "elemai_api_key_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "elemai_api_key", "show": true, "type": "str", "value": "", "l2_name": "elemai_api_key", "advanced": true, "password": true, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "elemai_api_key"}, "display_name": "Elemai Api Key"}, "elemai_base_url_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "elemai_base_url", "show": true, "type": "str", "value": "http://192.168.106.12:6001/v1/chatcompletion", "l2_name": "elemai_base_url", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "elemai_base_url"}, "display_name": "Elemai Base Url"}, "request_timeout_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "request_timeout", "show": false, "type": "float", "l2_name": "request_timeout", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "request_timeout"}, "display_name": "Request Timeout"}, "tiktoken_model_name_ProxyChatLLM-UYoB4": {"l2": false, "info": "", "list": false, "name": "tiktoken_model_name", "show": false, "type": "str", "l2_name": "tiktoken_model_name", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-UYoB4", "field": "tiktoken_model_name"}, "display_name": "Tiktoken Model Name"}, "metadata_WebBaseLoader-af6db": {"info": "", "list": false, "name": "metadata", "show": false, "type": "code", "value": "{}", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "display_name": "<PERSON><PERSON><PERSON>", "proxy": {"id": "WebBaseLoader-af6db", "field": "metadata"}}, "web_path_WebBaseLoader-af6db": {"l2": true, "info": "", "list": false, "name": "web_path", "show": true, "type": "str", "value": "https://dataelem.feishu.cn/wiki/ZxW6wZyAJicX4WkG0NqcWsbynde", "l2_name": "网址", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "display_name": "Web Page", "proxy": {"id": "WebBaseLoader-af6db", "field": "web_path"}}, "proxy_url_OpenAIProxyEmbedding-1771b": {"info": "", "list": false, "name": "proxy_url", "show": false, "type": "str", "value": "", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "http://*************:8080", "proxy": {"id": "OpenAIProxyEmbedding-1771b", "field": "proxy_url"}, "display_name": "Proxy Url"}}, "flow": {"data": {"nodes": [{"width": 384, "height": 376, "id": "CombineDocsChain-RCPNA", "type": "genericNode", "position": {"x": -1187.1604122631927, "y": -662.1420645633651}, "data": {"id": "CombineDocsChain-RCPNA", "node": {"template": {"llm": {"l2": false, "info": "", "list": false, "name": "llm", "show": true, "type": "BaseLanguageModel", "l2_name": "llm", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "display_name": "LLM"}, "_type": "load_qa_chain", "token_max": {"l2": false, "info": "当前只对stuff 生效", "list": false, "name": "token_max", "show": true, "type": "int", "value": -1, "l2_name": "token_max", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "token_max"}, "chain_type": {"l2": false, "info": "", "list": true, "name": "chain_type", "show": true, "type": "str", "value": "stuff", "l2_name": "chain_type", "options": ["stuff", "map_reduce", "map_rerank", "refine"], "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}}, "description": "Load question answering chain.", "base_classes": ["BaseCombineDocumentsChain", "function"], "display_name": "CombineDocsChain", "output_types": [], "custom_fields": {}, "documentation": ""}, "type": "CombineDocsChain", "value": null}, "selected": true, "positionAbsolute": {"x": -1187.1604122631927, "y": -662.1420645633651}}, {"width": 384, "height": 346, "id": "Milvus-CWpae", "type": "genericNode", "position": {"x": -1174.4689035846664, "y": -119.56186107292496}, "data": {"id": "Milvus-CWpae", "node": {"l2_name": "知识库", "template": {"_type": "<PERSON><PERSON><PERSON><PERSON>", "drop_old": {"l2": false, "info": "", "list": false, "name": "drop_old", "show": false, "type": "bool", "value": false, "l2_name": "drop_old", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "documents": {"l2": false, "info": "", "list": true, "name": "documents", "show": true, "type": "Document", "l2_name": "documents", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Documents"}, "embedding": {"l2": false, "info": "", "list": false, "name": "embedding", "show": true, "type": "Embeddings", "l2_name": "embedding", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "display_name": "Embedding"}, "metadatas": {"l2": false, "info": "", "list": true, "name": "metadatas", "show": false, "type": "code", "l2_name": "metadatas", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "index_params": {"l2": false, "info": "", "list": false, "name": "index_params", "show": false, "type": "code", "l2_name": "index_params", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "search_kwargs": {"l2": false, "info": "", "list": false, "name": "search_kwargs", "show": true, "type": "code", "value": "{}", "l2_name": "search_kwargs", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "search_params": {"l2": false, "info": "", "list": false, "name": "search_params", "show": false, "type": "code", "l2_name": "search_params", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "collection_name": {"l2": false, "info": "", "list": false, "name": "collection_name", "show": true, "type": "str", "value": "", "l2_name": "选择知识库", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "collection_id": ""}, "connection_args": {"l2": false, "info": "", "list": false, "name": "connection_args", "show": true, "type": "code", "value": "", "l2_name": "connection_args", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "consistency_level": {"l2": false, "info": "", "list": false, "name": "consistency_level", "show": false, "type": "str", "value": "Session", "l2_name": "consistency_level", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}}, "description": "Create a Milvus collection, indexes it with HNSW, and insert data.", "base_classes": ["<PERSON><PERSON><PERSON><PERSON>", "VectorStore", "BaseRetriever", "VectorStoreRetriever"], "display_name": "<PERSON><PERSON><PERSON><PERSON>", "output_types": [], "custom_fields": {}, "documentation": "http://***************:8030"}, "type": "<PERSON><PERSON><PERSON><PERSON>", "value": null}, "selected": true, "positionAbsolute": {"x": -1174.4689035846664, "y": -119.56186107292496}}, {"width": 384, "height": 316, "id": "RetrievalQA-x5nap", "type": "genericNode", "position": {"x": -510.8891021059987, "y": -499.2694176355641}, "data": {"id": "RetrievalQA-x5nap", "node": {"template": {"tags": {"l2": false, "info": "", "list": true, "name": "tags", "show": false, "type": "str", "l2_name": "tags", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "_type": "RetrievalQA", "memory": {"l2": false, "info": "", "list": false, "name": "memory", "show": true, "type": "BaseMemory", "l2_name": "memory", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "verbose": {"l2": false, "info": "", "list": false, "name": "verbose", "show": false, "type": "bool", "l2_name": "verbose", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "metadata": {"l2": false, "info": "", "list": false, "name": "metadata", "show": false, "type": "code", "l2_name": "metadata", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "callbacks": {"l2": false, "info": "", "list": true, "name": "callbacks", "show": false, "type": "langchain.callbacks.base.BaseCallbackHandler", "l2_name": "callbacks", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "input_key": {"l2": false, "info": "", "list": false, "name": "input_key", "show": true, "type": "str", "value": "query", "l2_name": "input_key", "advanced": true, "password": false, "required": true, "multiline": false, "placeholder": ""}, "retriever": {"l2": false, "info": "", "list": false, "name": "retriever", "show": true, "type": "BaseRetriever", "l2_name": "retriever", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}, "input_node": {"l2": false, "info": "", "list": false, "name": "input_node", "show": true, "type": "input", "l2_name": "input_node", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Preset Question"}, "output_key": {"l2": false, "info": "", "list": false, "name": "output_key", "show": true, "type": "str", "value": "result", "l2_name": "output_key", "advanced": true, "password": false, "required": true, "multiline": false, "placeholder": ""}, "combine_documents_chain": {"l2": false, "info": "", "list": false, "name": "combine_documents_chain", "show": true, "type": "BaseCombineDocumentsChain", "l2_name": "combine_documents_chain", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}, "return_source_documents": {"l2": false, "info": "", "list": false, "name": "return_source_documents", "show": true, "type": "bool", "value": true, "l2_name": "return_source_documents", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}}, "description": "Chain for question-answering against an index.", "base_classes": ["RetrievalQA", "Chain", "BaseRetrievalQA", "function"], "display_name": "RetrievalQA", "output_types": [], "custom_fields": {}, "documentation": "https://python.langchain.com/docs/modules/chains/popular/vector_db_qa"}, "type": "RetrievalQA", "value": null}, "selected": true, "positionAbsolute": {"x": -510.8891021059987, "y": -499.2694176355641}}, {"width": 384, "height": 500, "id": "ProxyChatLLM-UYoB4", "type": "genericNode", "position": {"x": -1789.0051284621989, "y": -759.1155283370271}, "data": {"id": "ProxyChatLLM-UYoB4", "node": {"template": {"n": {"l2": false, "info": "", "list": false, "name": "n", "show": false, "type": "int", "value": 1, "l2_name": "n", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "tags": {"l2": false, "info": "", "list": true, "name": "tags", "show": false, "type": "str", "l2_name": "tags", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "_type": "ProxyChatLLM", "cache": {"l2": false, "info": "", "list": false, "name": "cache", "show": false, "type": "bool", "l2_name": "cache", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "top_p": {"l2": false, "info": "", "list": false, "name": "top_p", "show": true, "type": "float", "value": 0.9, "l2_name": "top_p", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "client": {"l2": false, "info": "", "list": false, "name": "client", "show": false, "type": "Any", "l2_name": "client", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "headers": {"l2": false, "info": "", "list": false, "name": "headers", "show": false, "type": "code", "value": "", "l2_name": "headers", "advanced": false, "password": false, "required": false, "multiline": true, "placeholder": ""}, "verbose": {"l2": false, "info": "", "list": false, "name": "verbose", "show": false, "type": "bool", "value": false, "l2_name": "verbose", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "metadata": {"l2": false, "info": "", "list": false, "name": "metadata", "show": false, "type": "code", "l2_name": "metadata", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "callbacks": {"l2": false, "info": "", "list": true, "name": "callbacks", "show": false, "type": "langchain.callbacks.base.BaseCallbackHandler", "l2_name": "callbacks", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "streaming": {"l2": false, "info": "", "list": false, "name": "streaming", "show": false, "type": "bool", "value": false, "l2_name": "streaming", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "max_tokens": {"l2": false, "info": "", "list": false, "name": "max_tokens", "show": true, "type": "int", "value": "", "l2_name": "max_tokens", "advanced": true, "password": true, "required": false, "multiline": false, "placeholder": ""}, "model_name": {"l2": false, "info": "", "list": false, "name": "model_name", "show": true, "type": "str", "value": "gpt-4", "l2_name": "model_name", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "max_retries": {"l2": false, "info": "", "list": false, "name": "max_retries", "show": false, "type": "int", "value": 6, "l2_name": "max_retries", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "temperature": {"l2": false, "info": "", "list": false, "name": "temperature", "show": true, "type": "float", "value": 0.7, "l2_name": "temperature", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "model_kwargs": {"l2": false, "info": "", "list": false, "name": "model_kwargs", "show": true, "type": "code", "l2_name": "model_kwargs", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "elemai_api_key": {"l2": false, "info": "", "list": false, "name": "elemai_api_key", "show": true, "type": "str", "value": "", "l2_name": "elemai_api_key", "advanced": false, "password": true, "required": false, "multiline": false, "placeholder": ""}, "elemai_base_url": {"l2": false, "info": "", "list": false, "name": "elemai_base_url", "show": true, "type": "str", "value": "http://192.168.106.12:6001/v1/chatcompletion", "l2_name": "elemai_base_url", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "request_timeout": {"l2": false, "info": "", "list": false, "name": "request_timeout", "show": false, "type": "float", "l2_name": "request_timeout", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "tiktoken_model_name": {"l2": false, "info": "", "list": false, "name": "tiktoken_model_name", "show": false, "type": "str", "l2_name": "tiktoken_model_name", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}}, "description": "Wrapper around proxy Chat large language models.", "base_classes": ["ProxyChatLLM", "BaseLanguageModel", "BaseChatModel", "BaseLLM"], "display_name": "ProxyChatLLM", "output_types": [], "custom_fields": {}, "documentation": ""}, "type": "ProxyChatLLM", "value": null}, "selected": true, "positionAbsolute": {"x": -1789.0051284621989, "y": -759.1155283370271}}, {"width": 384, "height": 356, "id": "WebBaseLoader-af6db", "type": "genericNode", "position": {"x": -1699.1181246063038, "y": -135.15223886710186}, "data": {"id": "WebBaseLoader-af6db", "node": {"l2_name": "输入网址", "template": {"_type": "WebBaseLoader", "metadata": {"info": "", "list": false, "name": "metadata", "show": true, "type": "code", "value": "{}", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "display_name": "<PERSON><PERSON><PERSON>"}, "web_path": {"l2": true, "info": "", "list": false, "name": "web_path", "show": true, "type": "str", "value": "https://dataelem.feishu.cn/wiki/ZxW6wZyAJicX4WkG0NqcWsbynde", "l2_name": "网址", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "display_name": "Web Page"}}, "description": "Load HTML pages using `urllib` and parse them with `BeautifulSoup'.", "base_classes": ["Document"], "display_name": "WebBaseLoader", "output_types": ["Document"], "custom_fields": {}, "documentation": "https://python.langchain.com/docs/modules/data_connection/document_loaders/integrations/web_base"}, "type": "WebBaseLoader", "value": null}, "selected": true, "positionAbsolute": {"x": -1699.1181246063038, "y": -135.15223886710186}}, {"width": 384, "height": 172, "id": "OpenAIProxyEmbedding-1771b", "type": "genericNode", "position": {"x": -1686.4214049605273, "y": 261.6202500634144}, "data": {"id": "OpenAIProxyEmbedding-1771b", "node": {"template": {"_type": "proxy_embedding", "proxy_url": {"info": "", "list": false, "name": "proxy_url", "show": false, "type": "str", "value": "", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "http://*************:8080"}}, "description": " 使用自建的embedding服务使用openai进行embed ", "base_classes": ["Embeddings"], "display_name": "OpenAIProxyEmbedding", "output_types": [], "custom_fields": {}, "documentation": ""}, "type": "OpenAIProxyEmbedding", "value": null}, "selected": true, "positionAbsolute": {"x": -1686.4214049605273, "y": 261.6202500634144}}], "edges": [{"source": "CombineDocsChain-RCPNA", "target": "RetrievalQA-x5nap", "sourceHandle": "CombineDocsChain|CombineDocsChain-RCPNA|BaseCombineDocumentsChain|function", "targetHandle": "BaseCombineDocumentsChain|combine_documents_chain|RetrievalQA-x5nap", "id": "reactflow__edge-CombineDocsChain-RCPNACombineDocsChain|CombineDocsChain-RCPNA|BaseCombineDocumentsChain|function-RetrievalQA-x5napBaseCombineDocumentsChain|combine_documents_chain|RetrievalQA-x5nap", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "Milvus-CWpae", "target": "RetrievalQA-x5nap", "sourceHandle": "Milvus|Milvus-CWpae|Milvus|VectorStore|BaseRetriever|VectorStoreRetriever", "targetHandle": "BaseRetriever|retriever|RetrievalQA-x5nap", "id": "reactflow__edge-Milvus-CWpaeMilvus|Milvus-CWpae|Milvus|VectorStore|BaseRetriever|VectorStoreRetriever-RetrievalQA-x5napBaseRetriever|retriever|RetrievalQA-x5nap", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "ProxyChatLLM-UYoB4", "target": "CombineDocsChain-RCPNA", "sourceHandle": "ProxyChatLLM|ProxyChatLLM-UYoB4|ProxyChatLLM|BaseLanguageModel|BaseChatModel|BaseLLM", "targetHandle": "BaseLanguageModel|llm|CombineDocsChain-RCPNA", "id": "reactflow__edge-ProxyChatLLM-UYoB4ProxyChatLLM|ProxyChatLLM-UYoB4|ProxyChatLLM|BaseLanguageModel|BaseChatModel|BaseLLM-CombineDocsChain-RCPNABaseLanguageModel|llm|CombineDocsChain-RCPNA", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "WebBaseLoader-af6db", "target": "Milvus-CWpae", "sourceHandle": "WebBaseLoader|WebBaseLoader-af6db|Document", "targetHandle": "Document|documents|Milvus-CWpae", "id": "reactflow__edge-WebBaseLoader-af6dbWebBaseLoader|WebBaseLoader-af6db|Document-Milvus-CWpaeDocument|documents|Milvus-CWpae", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "OpenAIProxyEmbedding-1771b", "target": "Milvus-CWpae", "sourceHandle": "OpenAIProxyEmbedding|OpenAIProxyEmbedding-1771b|Embeddings", "targetHandle": "Embeddings|embedding|Milvus-CWpae", "id": "reactflow__edge-OpenAIProxyEmbedding-1771bOpenAIProxyEmbedding|OpenAIProxyEmbedding-1771b|Embeddings-Milvus-CWpaeEmbeddings|embedding|Milvus-CWpae", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}], "viewport": {"zoom": 1, "x": 0, "y": 0}}, "is_component": false, "name": "", "description": "", "id": "ddc2b", "status": 0, "write": false, "guide_word": ""}}}}, {"角色扮演": {"id": "LLMChain-2dd3b", "type": "<PERSON><PERSON><PERSON><PERSON>", "node": {"output_types": [], "display_name": "角色扮演", "documentation": "", "base_classes": ["Chain", "<PERSON><PERSON><PERSON><PERSON>", "function"], "description": "Chain to run queries against LLMs.", "template": {"tags_LLMChain-wRz0c": {"l2": false, "info": "", "list": true, "name": "tags", "show": false, "type": "str", "l2_name": "tags", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "LLMChain-wRz0c", "field": "tags"}, "display_name": "Tags"}, "verbose_LLMChain-wRz0c": {"l2": false, "info": "", "list": false, "name": "verbose", "show": false, "type": "bool", "value": false, "l2_name": "verbose", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "LLMChain-wRz0c", "field": "verbose"}, "display_name": "Verbose"}, "metadata_LLMChain-wRz0c": {"l2": false, "info": "", "list": false, "name": "metadata", "show": false, "type": "code", "l2_name": "metadata", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "LLMChain-wRz0c", "field": "metadata"}, "display_name": "<PERSON><PERSON><PERSON>"}, "callbacks_LLMChain-wRz0c": {"l2": false, "info": "", "list": true, "name": "callbacks", "show": false, "type": "langchain.callbacks.base.BaseCallbackHandler", "l2_name": "callbacks", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "LLMChain-wRz0c", "field": "callbacks"}, "display_name": "Callbacks"}, "input_node_LLMChain-wRz0c": {"l2": false, "info": "", "list": false, "name": "input_node", "show": true, "type": "input", "l2_name": "input_node", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Preset Question", "proxy": {"id": "LLMChain-wRz0c", "field": "input_node"}}, "llm_kwargs_LLMChain-wRz0c": {"l2": false, "info": "", "list": false, "name": "llm_kwargs", "show": false, "type": "code", "l2_name": "llm_kwargs", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "LLMChain-wRz0c", "field": "llm_kwargs"}, "display_name": "<PERSON><PERSON>"}, "output_key_LLMChain-wRz0c": {"l2": false, "info": "", "list": false, "name": "output_key", "show": true, "type": "str", "value": "text", "l2_name": "output_key", "advanced": true, "password": false, "required": true, "multiline": false, "placeholder": "", "proxy": {"id": "LLMChain-wRz0c", "field": "output_key"}, "display_name": "Output Key"}, "output_parser_LLMChain-wRz0c": {"l2": false, "info": "", "list": false, "name": "output_parser", "show": false, "type": "BaseLLMOutputParser", "l2_name": "output_parser", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "LLMChain-wRz0c", "field": "output_parser"}, "display_name": "Output Parser"}, "return_final_only_LLMChain-wRz0c": {"l2": false, "info": "", "list": false, "name": "return_final_only", "show": false, "type": "bool", "value": true, "l2_name": "return_final_only", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "LLMChain-wRz0c", "field": "return_final_only"}, "display_name": "Return Final Only"}, "input_PromptTemplate-ZoUVT": {"info": "", "list": false, "name": "input", "show": true, "type": "str", "value": "", "advanced": false, "password": false, "required": false, "multiline": true, "input_types": ["Document", "BaseOutputParser"], "placeholder": "", "display_name": "input", "proxy": {"id": "PromptTemplate-ZoUVT", "field": "input"}}, "template_PromptTemplate-ZoUVT": {"l2": false, "info": "", "list": false, "name": "template", "show": true, "type": "prompt", "value": "System：\n你将扮演一个科技公司的面试官，面试者是一名产品经理，提出 3 个犀利的问题。\n请注意：\n- 每次只问一个问题\n- 面试者回答问题后请简短点评一下，然后问下一个问题，不要试图纠正候选人的错误；\n- 如果你认为用户连续几次回答的都不对，就少问一点；\n- 问完最后一个问题后，你可以问这样一个问题：上一份工作为什么离职？用户回答该问题后，请表示理解与支持，并停止与面试者的对话。若面试者继续说话你可以礼貌拒绝。\n\n以下是你们的对话记录\n\n{chat_history}\n\n面试者：{input}\n\n你的回答：", "l2_name": "template", "advanced": false, "password": false, "required": true, "multiline": true, "placeholder": "", "proxy": {"id": "PromptTemplate-ZoUVT", "field": "template"}, "display_name": "Template"}, "chat_history_PromptTemplate-ZoUVT": {"info": "", "list": false, "name": "chat_history", "show": true, "type": "str", "value": "", "advanced": false, "password": false, "required": false, "multiline": true, "input_types": ["Document", "BaseOutputParser"], "placeholder": "", "display_name": "chat_history", "proxy": {"id": "PromptTemplate-ZoUVT", "field": "chat_history"}}, "output_parser_PromptTemplate-ZoUVT": {"l2": false, "info": "", "list": false, "name": "output_parser", "show": false, "type": "BaseOutputParser", "l2_name": "output_parser", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-ZoUVT", "field": "output_parser"}, "display_name": "Output Parser"}, "input_variables_PromptTemplate-ZoUVT": {"l2": false, "info": "", "list": true, "name": "input_variables", "show": false, "type": "str", "value": ["chat_history", "input"], "l2_name": "input_variables", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-ZoUVT", "field": "input_variables"}, "display_name": "Input Variables"}, "template_format_PromptTemplate-ZoUVT": {"l2": false, "info": "", "list": false, "name": "template_format", "show": false, "type": "str", "value": "f-string", "l2_name": "template_format", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-ZoUVT", "field": "template_format"}, "display_name": "Template Format"}, "partial_variables_PromptTemplate-ZoUVT": {"l2": false, "info": "", "list": false, "name": "partial_variables", "show": false, "type": "code", "l2_name": "partial_variables", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-ZoUVT", "field": "partial_variables"}, "display_name": "Partial Variables"}, "validate_template_PromptTemplate-ZoUVT": {"l2": false, "info": "", "list": false, "name": "validate_template", "show": false, "type": "bool", "value": true, "l2_name": "validate_template", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "PromptTemplate-ZoUVT", "field": "validate_template"}, "display_name": "Validate Template"}, "ai_prefix_ConversationBufferMemory-lI5Pt": {"l2": false, "info": "", "list": false, "name": "ai_prefix", "show": false, "type": "str", "value": "AI", "l2_name": "ai_prefix", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ConversationBufferMemory-lI5Pt", "field": "ai_prefix"}, "display_name": "Ai Prefix"}, "input_key_ConversationBufferMemory-lI5Pt": {"l2": false, "info": "The variable to be used as Chat Input when more than one variable is available.", "list": false, "name": "input_key", "show": true, "type": "str", "value": "", "l2_name": "input_key", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ConversationBufferMemory-lI5Pt", "field": "input_key"}, "display_name": "Input Key"}, "memory_key_ConversationBufferMemory-lI5Pt": {"l2": false, "info": "", "list": false, "name": "memory_key", "show": true, "type": "str", "value": "chat_history", "l2_name": "memory_key", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ConversationBufferMemory-lI5Pt", "field": "memory_key"}, "display_name": "Memory Key"}, "output_key_ConversationBufferMemory-lI5Pt": {"l2": false, "info": "The variable to be used as Chat Output (e.g. answer in a ConversationalRetrievalChain)", "list": false, "name": "output_key", "show": true, "type": "str", "value": "", "l2_name": "output_key", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ConversationBufferMemory-lI5Pt", "field": "output_key"}, "display_name": "Output Key"}, "chat_memory_ConversationBufferMemory-lI5Pt": {"l2": false, "info": "", "list": false, "name": "chat_memory", "show": true, "type": "BaseChatMessageHistory", "l2_name": "chat_memory", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ConversationBufferMemory-lI5Pt", "field": "chat_memory"}, "display_name": "Chat Memory"}, "human_prefix_ConversationBufferMemory-lI5Pt": {"l2": false, "info": "", "list": false, "name": "human_prefix", "show": false, "type": "str", "value": "Human", "l2_name": "human_prefix", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ConversationBufferMemory-lI5Pt", "field": "human_prefix"}, "display_name": "Human Prefix"}, "return_messages_ConversationBufferMemory-lI5Pt": {"l2": false, "info": "", "list": false, "name": "return_messages", "show": true, "type": "bool", "l2_name": "return_messages", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ConversationBufferMemory-lI5Pt", "field": "return_messages"}, "display_name": "Return Messages"}, "n_ProxyChatLLM-C0lLH": {"info": "", "list": false, "name": "n", "show": false, "type": "int", "value": 1, "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-C0lLH", "field": "n"}, "display_name": "N"}, "tags_ProxyChatLLM-C0lLH": {"info": "", "list": true, "name": "tags", "show": false, "type": "str", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-C0lLH", "field": "tags"}, "display_name": "Tags"}, "cache_ProxyChatLLM-C0lLH": {"info": "", "list": false, "name": "cache", "show": false, "type": "bool", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-C0lLH", "field": "cache"}, "display_name": "<PERSON><PERSON>"}, "top_p_ProxyChatLLM-C0lLH": {"info": "", "list": false, "name": "top_p", "show": true, "type": "float", "value": 0.9, "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-C0lLH", "field": "top_p"}, "display_name": "Top P"}, "client_ProxyChatLLM-C0lLH": {"info": "", "list": false, "name": "client", "show": false, "type": "Any", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-C0lLH", "field": "client"}, "display_name": "Client"}, "headers_ProxyChatLLM-C0lLH": {"info": "", "list": false, "name": "headers", "show": false, "type": "code", "value": "", "advanced": true, "password": false, "required": false, "multiline": true, "placeholder": "", "proxy": {"id": "ProxyChatLLM-C0lLH", "field": "headers"}, "display_name": "Headers"}, "verbose_ProxyChatLLM-C0lLH": {"info": "", "list": false, "name": "verbose", "show": false, "type": "bool", "value": false, "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-C0lLH", "field": "verbose"}, "display_name": "Verbose"}, "metadata_ProxyChatLLM-C0lLH": {"info": "", "list": false, "name": "metadata", "show": false, "type": "code", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-C0lLH", "field": "metadata"}, "display_name": "<PERSON><PERSON><PERSON>"}, "callbacks_ProxyChatLLM-C0lLH": {"info": "", "list": true, "name": "callbacks", "show": false, "type": "langchain.callbacks.base.BaseCallbackHandler", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-C0lLH", "field": "callbacks"}, "display_name": "Callbacks"}, "streaming_ProxyChatLLM-C0lLH": {"info": "", "list": false, "name": "streaming", "show": false, "type": "bool", "value": false, "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-C0lLH", "field": "streaming"}, "display_name": "Streaming"}, "max_tokens_ProxyChatLLM-C0lLH": {"info": "", "list": false, "name": "max_tokens", "show": true, "type": "int", "value": "", "advanced": true, "password": true, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-C0lLH", "field": "max_tokens"}, "display_name": "<PERSON>"}, "model_name_ProxyChatLLM-C0lLH": {"info": "", "list": false, "name": "model_name", "show": true, "type": "str", "value": "gpt-3.5-turbo-16k-0613", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-C0lLH", "field": "model_name"}, "display_name": "Model Name"}, "max_retries_ProxyChatLLM-C0lLH": {"info": "", "list": false, "name": "max_retries", "show": false, "type": "int", "value": 6, "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-C0lLH", "field": "max_retries"}, "display_name": "Max Retries"}, "temperature_ProxyChatLLM-C0lLH": {"info": "", "list": false, "name": "temperature", "show": true, "type": "float", "value": 0.7, "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-C0lLH", "field": "temperature"}, "display_name": "Temperature"}, "model_kwargs_ProxyChatLLM-C0lLH": {"info": "", "list": false, "name": "model_kwargs", "show": false, "type": "code", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-C0lLH", "field": "model_kwargs"}, "display_name": "Model Kwargs"}, "elemai_api_key_ProxyChatLLM-C0lLH": {"info": "", "list": false, "name": "elemai_api_key", "show": true, "type": "str", "value": "", "advanced": true, "password": true, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-C0lLH", "field": "elemai_api_key"}, "display_name": "Elemai Api Key"}, "elemai_base_url_ProxyChatLLM-C0lLH": {"info": "", "list": false, "name": "elemai_base_url", "show": true, "type": "str", "value": "http://192.168.106.12:6001/v1/chatcompletion", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-C0lLH", "field": "elemai_base_url"}, "display_name": "Elemai Base Url"}, "request_timeout_ProxyChatLLM-C0lLH": {"info": "", "list": false, "name": "request_timeout", "show": false, "type": "float", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-C0lLH", "field": "request_timeout"}, "display_name": "Request Timeout"}, "tiktoken_model_name_ProxyChatLLM-C0lLH": {"info": "", "list": false, "name": "tiktoken_model_name", "show": false, "type": "str", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": "", "proxy": {"id": "ProxyChatLLM-C0lLH", "field": "tiktoken_model_name"}, "display_name": "Tiktoken Model Name"}}, "flow": {"data": {"nodes": [{"width": 384, "height": 316, "id": "LLMChain-wRz0c", "type": "genericNode", "position": {"x": 621.1034022791711, "y": 113.49419945635839}, "data": {"id": "LLMChain-wRz0c", "node": {"template": {"llm": {"l2": false, "info": "", "list": false, "name": "llm", "show": true, "type": "BaseLanguageModel", "l2_name": "llm", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}, "tags": {"l2": false, "info": "", "list": true, "name": "tags", "show": false, "type": "str", "l2_name": "tags", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "_type": "<PERSON><PERSON><PERSON><PERSON>", "memory": {"l2": false, "info": "", "list": false, "name": "memory", "show": true, "type": "BaseMemory", "l2_name": "memory", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "prompt": {"l2": false, "info": "", "list": false, "name": "prompt", "show": true, "type": "BasePromptTemplate", "l2_name": "prompt", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}, "verbose": {"l2": false, "info": "", "list": false, "name": "verbose", "show": false, "type": "bool", "value": false, "l2_name": "verbose", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "metadata": {"l2": false, "info": "", "list": false, "name": "metadata", "show": false, "type": "code", "l2_name": "metadata", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "callbacks": {"l2": false, "info": "", "list": true, "name": "callbacks", "show": false, "type": "langchain.callbacks.base.BaseCallbackHandler", "l2_name": "callbacks", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "input_node": {"l2": false, "info": "", "list": false, "name": "input_node", "show": true, "type": "input", "l2_name": "input_node", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": "", "display_name": "Preset Question"}, "llm_kwargs": {"l2": false, "info": "", "list": false, "name": "llm_kwargs", "show": false, "type": "code", "l2_name": "llm_kwargs", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "output_key": {"l2": false, "info": "", "list": false, "name": "output_key", "show": true, "type": "str", "value": "text", "l2_name": "output_key", "advanced": true, "password": false, "required": true, "multiline": false, "placeholder": ""}, "output_parser": {"l2": false, "info": "", "list": false, "name": "output_parser", "show": false, "type": "BaseLLMOutputParser", "l2_name": "output_parser", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "return_final_only": {"l2": false, "info": "", "list": false, "name": "return_final_only", "show": false, "type": "bool", "value": true, "l2_name": "return_final_only", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}}, "description": "Chain to run queries against LLMs.", "base_classes": ["Chain", "<PERSON><PERSON><PERSON><PERSON>", "function"], "display_name": "<PERSON><PERSON><PERSON><PERSON>", "output_types": [], "custom_fields": {}, "documentation": "https://python.langchain.com/docs/modules/chains/foundational/llm_chain"}, "type": "<PERSON><PERSON><PERSON><PERSON>", "value": null}, "selected": true, "positionAbsolute": {"x": 621.1034022791711, "y": 113.49419945635839}}, {"width": 384, "height": 418, "id": "PromptTemplate-ZoUVT", "type": "genericNode", "position": {"x": -152.59469279870132, "y": 532.7389820028246}, "data": {"id": "PromptTemplate-ZoUVT", "node": {"name": "", "template": {"_type": "PromptTemplate", "input": {"info": "", "list": false, "name": "input", "show": true, "type": "str", "value": "", "advanced": false, "password": false, "required": false, "multiline": true, "input_types": ["Document", "BaseOutputParser"], "placeholder": "", "display_name": "input"}, "template": {"l2": false, "info": "", "list": false, "name": "template", "show": true, "type": "prompt", "value": "System：\n你将扮演一个科技公司的面试官，面试者是一名产品经理，提出 3 个犀利的问题。\n请注意：\n- 每次只问一个问题\n- 面试者回答问题后请简短点评一下，然后问下一个问题，不要试图纠正候选人的错误；\n- 如果你认为用户连续几次回答的都不对，就少问一点；\n- 问完最后一个问题后，你可以问这样一个问题：上一份工作为什么离职？用户回答该问题后，请表示理解与支持，并停止与面试者的对话。若面试者继续说话你可以礼貌拒绝。\n\n以下是你们的对话记录\n\n{chat_history}\n\n面试者：{input}\n\n你的回答：", "l2_name": "template", "advanced": false, "password": false, "required": true, "multiline": true, "placeholder": ""}, "chat_history": {"info": "", "list": false, "name": "chat_history", "show": true, "type": "str", "value": "", "advanced": false, "password": false, "required": false, "multiline": true, "input_types": ["Document", "BaseOutputParser"], "placeholder": "", "display_name": "chat_history"}, "output_parser": {"l2": false, "info": "", "list": false, "name": "output_parser", "show": false, "type": "BaseOutputParser", "l2_name": "output_parser", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "input_variables": {"l2": false, "info": "", "list": true, "name": "input_variables", "show": false, "type": "str", "value": ["chat_history", "input"], "l2_name": "input_variables", "advanced": false, "password": false, "required": true, "multiline": false, "placeholder": ""}, "template_format": {"l2": false, "info": "", "list": false, "name": "template_format", "show": false, "type": "str", "value": "f-string", "l2_name": "template_format", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "partial_variables": {"l2": false, "info": "", "list": false, "name": "partial_variables", "show": false, "type": "code", "l2_name": "partial_variables", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "validate_template": {"l2": false, "info": "", "list": false, "name": "validate_template", "show": false, "type": "bool", "value": true, "l2_name": "validate_template", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}}, "description": "<PERSON><PERSON><PERSON> to represent a prompt for an LLM.", "base_classes": ["PromptTemplate", "StringPromptTemplate", "BasePromptTemplate"], "display_name": "PromptTemplate", "output_types": [], "custom_fields": {"template": ["chat_history", "input"]}, "documentation": "https://python.langchain.com/docs/modules/model_io/prompts/prompt_templates/", "field_formatters": {"formatters": {"openai_api_key": {}}, "base_formatters": {"dict": {}, "list": {}, "show": {}, "union": {}, "kwargs": {}, "default": {}, "headers": {}, "optional": {}, "password": {}, "multiline": {}, "model_fields": {"MODEL_DICT": {"OpenAI": ["text-davinci-003", "text-davinci-002", "text-curie-001", "text-babbage-001", "text-ada-001"], "Anthropic": ["claude-v1", "claude-v1-100k", "claude-instant-v1", "claude-instant-v1-100k", "claude-v1.3", "claude-v1.3-100k", "claude-v1.2", "claude-v1.0", "claude-instant-v1.1", "claude-instant-v1.1-100k", "claude-instant-v1.0"], "ChatOpenAI": ["gpt-3.5-turbo-0613", "gpt-3.5-turbo", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-16k", "gpt-4-0613", "gpt-4-32k-0613", "gpt-4", "gpt-4-32k"], "ChatAnthropic": ["claude-v1", "claude-v1-100k", "claude-instant-v1", "claude-instant-v1-100k", "claude-v1.3", "claude-v1.3-100k", "claude-v1.2", "claude-v1.0", "claude-instant-v1.1", "claude-instant-v1.1-100k", "claude-instant-v1.0"]}}, "dict_code_file": {}}}}, "type": "PromptTemplate", "value": null}, "selected": true, "positionAbsolute": {"x": -152.59469279870132, "y": 532.7389820028246}}, {"width": 384, "height": 526, "id": "ConversationBufferMemory-lI5Pt", "type": "genericNode", "position": {"x": -556.5758769509117, "y": -30.001495256074236}, "data": {"id": "ConversationBufferMemory-lI5Pt", "node": {"template": {"_type": "ConversationBufferMemory", "ai_prefix": {"l2": false, "info": "", "list": false, "name": "ai_prefix", "show": false, "type": "str", "value": "AI", "l2_name": "ai_prefix", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "input_key": {"l2": false, "info": "The variable to be used as Chat Input when more than one variable is available.", "list": false, "name": "input_key", "show": true, "type": "str", "value": "", "l2_name": "input_key", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "memory_key": {"l2": false, "info": "", "list": false, "name": "memory_key", "show": true, "type": "str", "value": "chat_history", "l2_name": "memory_key", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "output_key": {"l2": false, "info": "The variable to be used as Chat Output (e.g. answer in a ConversationalRetrievalChain)", "list": false, "name": "output_key", "show": true, "type": "str", "value": "", "l2_name": "output_key", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "chat_memory": {"l2": false, "info": "", "list": false, "name": "chat_memory", "show": true, "type": "BaseChatMessageHistory", "l2_name": "chat_memory", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "human_prefix": {"l2": false, "info": "", "list": false, "name": "human_prefix", "show": false, "type": "str", "value": "Human", "l2_name": "human_prefix", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "return_messages": {"l2": false, "info": "", "list": false, "name": "return_messages", "show": true, "type": "bool", "l2_name": "return_messages", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}}, "description": "Buffer for storing conversation memory.", "base_classes": ["BaseChatMemory", "ConversationBufferMemory", "BaseMemory"], "display_name": "ConversationBufferMemory", "output_types": [], "custom_fields": {}, "documentation": "https://python.langchain.com/docs/modules/memory/how_to/buffer"}, "type": "ConversationBufferMemory", "value": null}, "selected": true, "positionAbsolute": {"x": -556.5758769509117, "y": -30.001495256074236}}, {"width": 384, "height": 500, "id": "ProxyChatLLM-C0lLH", "type": "genericNode", "position": {"x": -26.90943659439222, "y": -434.6186906908237}, "data": {"id": "ProxyChatLLM-C0lLH", "node": {"template": {"n": {"info": "", "list": false, "name": "n", "show": false, "type": "int", "value": 1, "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "tags": {"info": "", "list": true, "name": "tags", "show": false, "type": "str", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "_type": "ProxyChatLLM", "cache": {"info": "", "list": false, "name": "cache", "show": false, "type": "bool", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "top_p": {"info": "", "list": false, "name": "top_p", "show": true, "type": "float", "value": 0.9, "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "client": {"info": "", "list": false, "name": "client", "show": false, "type": "Any", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "headers": {"info": "", "list": false, "name": "headers", "show": false, "type": "code", "value": "", "advanced": false, "password": false, "required": false, "multiline": true, "placeholder": ""}, "verbose": {"info": "", "list": false, "name": "verbose", "show": false, "type": "bool", "value": false, "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "metadata": {"info": "", "list": false, "name": "metadata", "show": false, "type": "code", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "callbacks": {"info": "", "list": true, "name": "callbacks", "show": false, "type": "langchain.callbacks.base.BaseCallbackHandler", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "streaming": {"info": "", "list": false, "name": "streaming", "show": false, "type": "bool", "value": false, "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "max_tokens": {"info": "", "list": false, "name": "max_tokens", "show": true, "type": "int", "value": "", "advanced": true, "password": true, "required": false, "multiline": false, "placeholder": ""}, "model_name": {"info": "", "list": false, "name": "model_name", "show": true, "type": "str", "value": "gpt-3.5-turbo-16k-0613", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "max_retries": {"info": "", "list": false, "name": "max_retries", "show": false, "type": "int", "value": 6, "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "temperature": {"info": "", "list": false, "name": "temperature", "show": true, "type": "float", "value": 0.7, "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "model_kwargs": {"info": "", "list": false, "name": "model_kwargs", "show": true, "type": "code", "advanced": true, "password": false, "required": false, "multiline": false, "placeholder": ""}, "elemai_api_key": {"info": "", "list": false, "name": "elemai_api_key", "show": true, "type": "str", "value": "", "advanced": false, "password": true, "required": false, "multiline": false, "placeholder": ""}, "elemai_base_url": {"info": "", "list": false, "name": "elemai_base_url", "show": true, "type": "str", "value": "http://192.168.106.12:6001/v1/chatcompletion", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "request_timeout": {"info": "", "list": false, "name": "request_timeout", "show": false, "type": "float", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}, "tiktoken_model_name": {"info": "", "list": false, "name": "tiktoken_model_name", "show": false, "type": "str", "advanced": false, "password": false, "required": false, "multiline": false, "placeholder": ""}}, "description": "Wrapper around proxy Chat large language models.", "base_classes": ["ProxyChatLLM", "BaseLanguageModel", "BaseChatModel", "BaseLLM"], "display_name": "ProxyChatLLM", "output_types": [], "custom_fields": {}, "documentation": ""}, "type": "ProxyChatLLM", "value": null}, "selected": true, "positionAbsolute": {"x": -26.90943659439222, "y": -434.6186906908237}}], "edges": [{"source": "PromptTemplate-ZoUVT", "target": "LLMChain-wRz0c", "sourceHandle": "PromptTemplate|PromptTemplate-ZoUVT|PromptTemplate|StringPromptTemplate|BasePromptTemplate", "targetHandle": "BasePromptTemplate|prompt|LLMChain-wRz0c", "id": "reactflow__edge-PromptTemplate-ZoUVTPromptTemplate|PromptTemplate-ZoUVT|PromptTemplate|StringPromptTemplate|BasePromptTemplate-LLMChain-wRz0cBasePromptTemplate|prompt|LLMChain-wRz0c", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "ConversationBufferMemory-lI5Pt", "target": "LLMChain-wRz0c", "sourceHandle": "ConversationBufferMemory|ConversationBufferMemory-lI5Pt|BaseChatMemory|ConversationBufferMemory|BaseMemory", "targetHandle": "BaseMemory|memory|LLMChain-wRz0c", "id": "reactflow__edge-ConversationBufferMemory-lI5PtConversationBufferMemory|ConversationBufferMemory-lI5Pt|BaseChatMemory|ConversationBufferMemory|BaseMemory-LLMChain-wRz0cBaseMemory|memory|LLMChain-wRz0c", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}, {"source": "ProxyChatLLM-C0lLH", "target": "LLMChain-wRz0c", "sourceHandle": "ProxyChatLLM|ProxyChatLLM-C0lLH|ProxyChatLLM|BaseLanguageModel|BaseChatModel|BaseLLM", "targetHandle": "BaseLanguageModel|llm|LLMChain-wRz0c", "id": "reactflow__edge-ProxyChatLLM-C0lLHProxyChatLLM|ProxyChatLLM-C0lLH|ProxyChatLLM|BaseLanguageModel|BaseChatModel|BaseLLM-LLMChain-wRz0cBaseLanguageModel|llm|LLMChain-wRz0c", "style": {"stroke": "#555"}, "className": "stroke-gray-900 ", "animated": false, "selected": true}], "viewport": {"zoom": 1, "x": 0, "y": 0}}, "is_component": false, "name": "", "description": "", "id": "32a69", "status": 0, "write": false, "guide_word": ""}}}}]